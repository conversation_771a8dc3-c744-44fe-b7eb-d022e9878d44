# AI Hedge Fund GUI

这是 AI Hedge Fund 项目的图形用户界面实现。

## 文档结构

- [需求分析](./docs/requirements.md)
- [架构设计](./docs/architecture.md)

## 目录结构

```
app/windows/
├── docs/                   # 文档目录
│   ├── requirements.md    # 需求分析文档
│   └── architecture.md    # 架构设计文档
├── src/                   # 源代码目录
│   ├── main_window.py    # 主窗口
│   ├── views/            # 视图组件
│   ├── controllers/      # 控制器
│   ├── models/           # 数据模型
│   └── api/              # API客户端
├── test/                 # 测试目录
│   ├── README.md         # 测试说明文档
│   ├── run_tests.py      # 测试运行器
│   └── test_*.py         # 各种测试脚本
├── run.py               # 主启动脚本
├── requirements.txt     # Python依赖
├── pyproject.toml       # Poetry项目配置
└── README.md            # 项目说明文档
```

## 开发环境

- Python 3.11+
- Poetry 1.4.0+

## 快速开始

1. 安装 Poetry（如果尚未安装）：
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. 安装依赖：
```bash
cd ui
poetry install
```

3. 运行程序：
```bash
poetry run python src/main_window.py
```

## 开发指南

### 添加新依赖

```bash
# 添加生产依赖
poetry add package-name

# 添加开发依赖
poetry add --group dev package-name
```

### 代码格式化

```bash
# 使用 black 格式化代码
poetry run black src tests

# 使用 isort 排序导入
poetry run isort src tests
```

### 代码检查

```bash
# 运行 flake8 检查
poetry run flake8 src tests

# 运行 mypy 类型检查
poetry run mypy src tests
```

### 运行测试

```bash
# 运行所有测试
cd test
python run_tests.py

# 运行特定测试
python run_tests.py api_connection

# 手动运行单个测试
python test_loading_states.py
```

## 项目配置

项目使用 Poetry 进行依赖管理，主要配置文件为 `pyproject.toml`，包含：

- 项目元数据
- 依赖管理
- 开发工具配置
  - black（代码格式化）
  - isort（导入排序）
  - flake8（代码检查）
  - mypy（类型检查）
  - pytest（测试框架）

## 安装说明

1. 确保已安装 Python 3.8 或更高版本

2. 安装依赖：
```bash
cd app/windows
pip install -r requirements.txt
```

## 运行说明

1. 启动程序：
```bash
python run.py
```

2. 使用说明：
   - 在左侧导航树中选择"分析" -> "股票分析"
   - 在顶部选择市场（美股/A股/加密货币）
   - 输入股票代码（多个代码用逗号分隔）
   - 选择日期范围
   - 点击"分析"按钮

## 功能说明

1. 市场支持：
   - 美股市场
   - A股市场
   - 加密货币市场

2. 分析功能：
   - 技术分析
   - 基本面分析
   - 情绪分析
   - 估值分析
   - 投资组合分析

3. 数据缓存：
   - 自动缓存查询结果
   - 缓存有效期1小时
   - 程序关闭时自动清理缓存

## 注意事项

1. 美股数据：
   - AAPL、GOOGL、MSFT、NVDA、TSLA 免费
   - 其他股票需要设置 FINANCIAL_DATASETS_API_KEY

2. A股数据：
   - 使用 akshare 获取数据
   - 需要稳定的网络连接

3. 加密货币数据：
   - 使用 ccxt 获取数据
   - 支持主流交易所 