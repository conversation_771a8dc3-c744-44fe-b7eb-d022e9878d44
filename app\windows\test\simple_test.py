#!/usr/bin/env python3
"""
简单测试
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
    from PyQt6.QtCore import QTimer
    from views.stock.single_stock_single_agent import SingleStockSingleAgentPage
    
    class SimpleTestWindow(QMainWindow):
        """简单测试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("简单测试")
            self.setGeometry(100, 100, 1200, 800)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # 创建单股单顾问页面
            self.page = SingleStockSingleAgentPage()
            layout.addWidget(self.page)
            
            # 延迟设置测试数据
            QTimer.singleShot(1000, self.setup_test_data)
        
        def setup_test_data(self):
            """设置测试数据"""
            print("=== 设置测试数据 ===")
            
            # 设置市场列表
            market_list = {
                "SH": "上海证券交易所",
                "SZ": "深圳证券交易所"
            }
            self.page.set_market_list(market_list)
            print(f"设置市场列表: {market_list}")
            
            # 设置股票列表
            stock_list = {
                "600519": "贵州茅台",
                "000001": "平安银行"
            }
            self.page.set_stock_list("SH", stock_list)
            print(f"设置股票列表: {stock_list}")

    def main():
        """主函数"""
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = SimpleTestWindow()
        window.show()
        
        print("简单测试已启动")
        print("观察控制台输出，检查所有调试信息")
        
        sys.exit(app.exec())

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
