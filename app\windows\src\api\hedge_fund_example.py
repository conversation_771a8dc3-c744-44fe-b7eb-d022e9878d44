"""
对冲基金API客户端使用示例
演示如何使用HedgeFundClient进行对冲基金分析
"""

from hedge_fund_client import HedgeFundClient


def example_basic_usage():
    """基本使用示例"""
    print("=== 对冲基金API客户端基本使用示例 ===")
    
    # 创建客户端实例
    client = HedgeFundClient("http://localhost:8000")
    
    # 检查API连接
    if not client.health_check():
        print("❌ API服务器连接失败")
        return
    
    print("✅ API服务器连接成功")
    
    # 获取可用的分析师代理
    agents_response = client.get_agents()
    print(f"可用代理数量: {len(agents_response.get('agents', []))}")
    
    # 获取可用的语言模型
    models_response = client.get_language_models()
    print(f"可用模型数量: {len(models_response.get('models', []))}")
    
    # 验证请求参数
    validation = client.validate_hedge_fund_request(
        tickers=["AAPL", "GOOGL"],
        selected_agents=["warren_buffett", "peter_lynch"]
    )
    
    if validation["is_valid"]:
        print("✅ 请求参数验证通过")
    else:
        print("❌ 请求参数验证失败:")
        for error in validation["errors"]:
            print(f"  - {error}")
        return
    
    # 获取默认分析时间段
    period = client.get_default_analysis_period()
    print(f"默认分析时间段: {period['start_date']} 到 {period['end_date']}")


def example_sync_analysis():
    """同步分析示例"""
    print("\n=== 同步分析示例 ===")
    
    client = HedgeFundClient("http://localhost:8000")
    
    # 执行同步分析
    result = client.run_hedge_fund_sync(
        tickers=["AAPL", "GOOGL", "MSFT"],
        selected_agents=["warren_buffett", "peter_lynch", "charlie_munger"],
        model_name="gpt-4o",
        model_provider="openai",
        initial_cash=100000.0,
        margin_requirement=0.0
    )
    
    if "error" in result:
        print(f"❌ 分析失败: {result['error']}")
    else:
        print("✅ 分析完成")
        print(f"决策数量: {len(result.get('decisions', {}))}")
        print(f"分析师信号数量: {len(result.get('analyst_signals', {}))}")


def example_streaming_analysis():
    """流式分析示例"""
    print("\n=== 流式分析示例 ===")
    
    client = HedgeFundClient("http://localhost:8000")
    
    # 定义回调函数
    def progress_callback(event):
        event_type = event.get("type")
        data = event.get("data", {})
        
        if event_type == "start":
            print("🚀 开始分析...")
        elif event_type == "progress":
            agent = data.get("agent", "未知")
            ticker = data.get("ticker", "未知")
            status = data.get("status", "未知")
            print(f"📊 {agent} 正在分析 {ticker}: {status}")
    
    def complete_callback(result):
        print("✅ 分析完成!")
        print(f"决策数量: {len(result.get('decisions', {}))}")
        print(f"分析师信号数量: {len(result.get('analyst_signals', {}))}")
    
    def error_callback(error_msg):
        print(f"❌ 分析错误: {error_msg}")
    
    # 执行流式分析
    result = client.run_hedge_fund_streaming(
        tickers=["AAPL", "GOOGL"],
        selected_agents=["warren_buffett", "peter_lynch"],
        model_name="gpt-4o",
        model_provider="openai",
        initial_cash=100000.0,
        margin_requirement=0.0,
        progress_callback=progress_callback,
        complete_callback=complete_callback,
        error_callback=error_callback
    )
    
    if "error" in result:
        print(f"❌ 流式分析失败: {result['error']}")


def example_advanced_usage():
    """高级使用示例"""
    print("\n=== 高级使用示例 ===")
    
    client = HedgeFundClient("http://localhost:8000")
    
    # 自定义代理模型配置
    agent_models = [
        {
            "agent_id": "warren_buffett",
            "model_name": "gpt-4o",
            "model_provider": "openai"
        },
        {
            "agent_id": "peter_lynch",
            "model_name": "claude-3-sonnet",
            "model_provider": "anthropic"
        }
    ]
    
    # 自定义时间范围
    start_date = "2024-01-01"
    end_date = "2024-03-31"
    
    # 执行分析
    result = client.run_hedge_fund_sync(
        tickers=["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA"],
        selected_agents=["warren_buffett", "peter_lynch", "charlie_munger"],
        agent_models=agent_models,
        start_date=start_date,
        end_date=end_date,
        model_name="gpt-4o",
        model_provider="openai",
        initial_cash=500000.0,
        margin_requirement=0.1
    )
    
    if "error" in result:
        print(f"❌ 高级分析失败: {result['error']}")
    else:
        print("✅ 高级分析完成")
        decisions = result.get('decisions', {})
        signals = result.get('analyst_signals', {})
        
        print(f"分析时间段: {start_date} 到 {end_date}")
        print(f"初始资金: $500,000")
        print(f"保证金要求: 10%")
        print(f"投资决策数量: {len(decisions)}")
        print(f"分析师信号数量: {len(signals)}")


if __name__ == "__main__":
    # 运行所有示例
    example_basic_usage()
    example_sync_analysis()
    example_streaming_analysis()
    example_advanced_usage() 