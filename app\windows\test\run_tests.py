#!/usr/bin/env python3
"""
测试运行器
统一运行所有测试脚本
"""

import sys
import os
import subprocess
import glob

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)


def run_test(test_file):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {os.path.basename(test_file)}")
    print(f"{'='*60}")
    
    try:
        # 运行测试文件
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=30)
        
        if result.returncode == 0:
            print("✓ 测试通过")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("✗ 测试失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
            if result.stdout:
                print("输出:")
                print(result.stdout)
                
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("✗ 测试超时")
        return False
    except Exception as e:
        print(f"✗ 运行测试时出错: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("AI量化交易系统 - Windows应用测试套件")
    print("="*60)
    
    # 获取所有测试文件
    test_files = glob.glob("test_*.py")
    
    if not test_files:
        print("未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件:")
    for test_file in test_files:
        print(f"  - {test_file}")
    
    # 按类型分组测试
    unit_tests = []
    integration_tests = []
    ui_tests = []
    
    for test_file in test_files:
        if "loading_states" in test_file or "main_controller" in test_file:
            ui_tests.append(test_file)
        elif "connection" in test_file or "ping" in test_file:
            integration_tests.append(test_file)
        else:
            unit_tests.append(test_file)
    
    # 运行测试
    passed = 0
    failed = 0
    
    # 1. 单元测试
    if unit_tests:
        print(f"\n{'='*60}")
        print("运行单元测试")
        print(f"{'='*60}")
        
        for test_file in unit_tests:
            if run_test(test_file):
                passed += 1
            else:
                failed += 1
    
    # 2. 集成测试
    if integration_tests:
        print(f"\n{'='*60}")
        print("运行集成测试")
        print(f"{'='*60}")
        
        for test_file in integration_tests:
            if run_test(test_file):
                passed += 1
            else:
                failed += 1
    
    # 3. UI测试（需要用户交互，单独运行）
    if ui_tests:
        print(f"\n{'='*60}")
        print("UI测试（需要手动运行）")
        print(f"{'='*60}")
        
        for test_file in ui_tests:
            print(f"  - {test_file} (需要手动运行: python test/{test_file})")
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if ui_tests:
        print(f"UI测试: {len(ui_tests)} (需要手动运行)")
    
    if failed == 0:
        print("\n🎉 所有自动化测试都通过了！")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
    
    return failed == 0


def run_specific_test(test_name):
    """运行特定的测试"""
    test_file = f"test_{test_name}.py"
    if os.path.exists(test_file):
        return run_test(test_file)
    else:
        print(f"测试文件不存在: {test_file}")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 运行特定测试
        test_name = sys.argv[1]
        run_specific_test(test_name)
    else:
        # 运行所有测试
        run_all_tests()
