#!/usr/bin/env python3
"""
API连通性检查测试脚本
验证只调用/ping进行API连通性检查，不触发其他API调用
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from api.client import APIClient
from api.stock_data_client import StockDataClient


def test_ping_only():
    """测试只使用ping进行连通性检查"""
    print("=" * 60)
    print("API连通性检查测试")
    print("=" * 60)
    
    # 测试1: 使用基础APIClient进行健康检查
    print("\n1. 使用基础APIClient进行健康检查...")
    try:
        client = APIClient("http://localhost:8000")
        print("✓ 基础APIClient创建成功")
        
        # 进行健康检查
        print("  正在调用健康检查...")
        is_healthy = client.health_check()
        print(f"  健康检查结果: {'正常' if is_healthy else '异常'}")
        
    except Exception as e:
        print(f"✗ 基础APIClient健康检查失败: {str(e)}")
    
    # 测试2: 使用StockDataClient进行健康检查
    print("\n2. 使用StockDataClient进行健康检查...")
    try:
        client = StockDataClient("http://localhost:8000")
        print("✓ StockDataClient创建成功")
        
        # 进行健康检查
        print("  正在调用健康检查...")
        is_healthy = client.health_check()
        print(f"  健康检查结果: {'正常' if is_healthy else '异常'}")
        
    except Exception as e:
        print(f"✗ StockDataClient健康检查失败: {str(e)}")
    
    # 测试3: 对比两种方式的API调用
    print("\n3. 对比API调用方式...")
    print("  基础APIClient健康检查调用: GET / 或 GET /ping")
    print("  StockDataClient健康检查调用: GET / 或 GET /ping")
    print("  两者都应该只调用健康检查端点，不触发其他API调用")
    
    print("\n" + "=" * 60)
    print("✓ 连通性检查测试完成")
    print("=" * 60)


def test_initialization_flow():
    """测试初始化流程"""
    print("\n" + "=" * 60)
    print("初始化流程测试")
    print("=" * 60)
    
    # 测试1: 模拟主控制器的API检查流程
    print("\n1. 模拟主控制器的API检查流程...")
    try:
        # 第一步：使用基础APIClient检查连通性
        print("  第一步：使用基础APIClient检查连通性...")
        base_client = APIClient("http://localhost:8000")
        is_healthy = base_client.health_check()
        print(f"  连通性检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            # 第二步：如果连通性正常，创建StockDataInitializer
            print("  第二步：连通性正常，创建StockDataInitializer...")
            from models.initializer import StockDataInitializer
            initializer = StockDataInitializer("http://localhost:8000")
            print("  ✓ StockDataInitializer创建成功")
            
            # 第三步：运行初始化（这里只是测试，不实际运行）
            print("  第三步：准备运行初始化...")
            print("  ✓ 初始化流程准备就绪")
        else:
            print("  第二步：连通性异常，程序应该退出")
            
    except Exception as e:
        print(f"✗ 初始化流程测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 初始化流程测试完成")
    print("=" * 60)


def main():
    """主函数"""
    print("AI Hedge Fund - API连通性检查测试")
    
    # 测试ping检查
    test_ping_only()
    
    # 测试初始化流程
    test_initialization_flow()
    
    print("\n🎉 所有测试完成！")
    print("\n总结:")
    print("1. 启动时只调用 /ping 或 / 进行连通性检查")
    print("2. 使用基础APIClient，避免触发其他API调用")
    print("3. 只有连通性检查通过后，才创建StockDataInitializer")
    print("4. 确保不会在连通性检查阶段调用 /api/stock-data/markets")


if __name__ == "__main__":
    main() 