# API架构说明

## 概述

本项目已经将Windows应用中的股票数据获取方法迁移到backend中，并创建了独立的API调用抽象层。Windows应用现在通过API调用获取股票数据，而不是直接调用数据源。

## 架构设计

### 1. Backend API (app/backend)

#### 新增文件
- `routes/stock_data.py` - 股票数据API路由
- `models/schemas.py` - 新增股票数据响应模型

#### API端点
- `GET /api/stock-data/markets` - 获取可用市场列表
- `GET /api/stock-data/stocks/{market}` - 获取指定市场的股票列表
- `GET /api/stock-data/price/{market}/{symbol}` - 获取股票价格数据
- `GET /api/stock-data/financial/{market}/{symbol}` - 获取财务指标数据
- `GET /api/stock-data/company/{market}/{symbol}` - 获取公司信息
- `GET /api/stock-data/news/{market}/{symbol}` - 获取新闻数据
- `GET /api/stock-data/insider-trades/{market}/{symbol}` - 获取内部交易数据
- `GET /api/stock-data/market-info/{market}` - 获取市场信息
- `GET /api/stock-data/validate/{market}/{symbol}` - 验证股票代码

### 2. Windows API客户端 (app/windows/src/api)

#### 新增文件夹结构
```
api/
├── __init__.py
├── client.py              # 基础API客户端
├── stock_data_client.py   # 股票数据API客户端
└── config/
    └── api_config.py      # API配置文件
```

#### 主要类
- `APIClient` - 基础HTTP客户端，提供通用的请求方法
- `StockDataClient` - 股票数据专用客户端，继承自APIClient
- `APIConfig` - API配置管理类

### 3. 更新的Windows组件

#### 更新的文件
- `models/market_manager.py` - 使用API客户端获取数据
- `models/initializer.py` - 使用API客户端初始化数据
- `views/stock/stock_input.py` - 使用新的MarketManager
- `views/stock/multi_stock_input.py` - 使用新的MarketManager
- `controllers/main_controller.py` - 使用新的初始化器

## 使用方法

### 1. 启动Backend服务

```bash
cd app/backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 配置API连接

#### 方法1: 环境变量
```bash
export AI_HEDGE_FUND_API_URL="http://localhost:8000"
export AI_HEDGE_FUND_API_TIMEOUT="30"
export AI_HEDGE_FUND_API_RETRY_COUNT="3"
```

#### 方法2: 配置文件
创建 `api_config.json`:
```json
{
  "base_url": "http://localhost:8000",
  "timeout": 30,
  "retry_count": 3,
  "enable_cache": true
}
```

### 3. 运行Windows应用

```bash
cd app/windows
python run_main.py
```

## API调用示例

### 获取股票列表
```python
from api.stock_data_client import StockDataClient

client = StockDataClient("http://localhost:8000")
stocks = client.get_stock_list("US")
print(stocks)
```

### 获取价格数据
```python
price_data = client.get_price_data("US", "AAPL", "2024-01-01", "2024-01-31")
print(price_data)
```

### 获取财务指标
```python
metrics = client.get_financial_metrics("US", "AAPL", "2024-01-31")
print(metrics)
```

## 错误处理

### 1. API连接失败
当API服务器不可用时，系统会自动使用默认数据：
- 美股：AAPL, MSFT, GOOGL等10只股票
- A股：600519, 000858等10只股票
- 加密货币：BTC/USDT, ETH/USDT等10个交易对

**用户界面提示**：
- 状态栏显示连接状态
- 状态指示器显示连接状态（绿色=已连接，橙色=未连接，红色=错误）
- 弹出友好的错误提示对话框，指导用户检查网络连接

### 2. 离线模式
系统支持完全离线运行：
- 应用启动时检查API连接
- 连接失败时自动切换到离线模式
- 使用内置的默认股票数据
- 界面正常显示，用户可以正常使用基本功能

### 3. 网络超时
- 默认超时时间：30秒
- 自动重试：3次
- 重试间隔：1秒

### 4. 数据验证
- 本地验证：检查股票代码格式
- API验证：通过后端验证股票代码有效性

## 缓存机制

### 1. 客户端缓存
- 股票列表缓存：避免重复请求
- 价格数据缓存：1小时有效期
- 财务指标缓存：1小时有效期

### 2. 缓存配置
```python
from config.api_config import api_config

# 启用/禁用缓存
api_config.set("enable_cache", True)

# 设置缓存时间
api_config.set("cache_duration", 3600)  # 1小时
```

## 扩展性

### 1. 添加新的API端点
1. 在 `app/backend/routes/stock_data.py` 中添加新路由
2. 在 `app/backend/models/schemas.py` 中添加响应模型
3. 在 `app/windows/src/api/stock_data_client.py` 中添加对应方法

### 2. 添加新的数据源
1. 在 `src/market/` 中添加新的市场提供者
2. 在 `src/market/factory.py` 中注册新的提供者
3. 更新API路由以支持新的市场类型

### 3. 添加新的客户端功能
1. 继承 `APIClient` 创建新的客户端类
2. 实现特定的API调用方法
3. 在Windows组件中使用新的客户端

## 性能优化

### 1. 连接池
- 使用 `requests.Session()` 复用连接
- 自动管理连接生命周期

### 2. 异步处理
- 初始化过程在后台线程执行
- 不阻塞UI线程

### 3. 数据预加载
- 启动时预加载常用股票数据
- 按需加载详细数据

## 监控和日志

### 1. 健康检查
```python
# 检查API服务器状态
is_healthy = client.health_check()
print(f"API服务器状态: {'正常' if is_healthy else '异常'}")
```

### 2. 错误日志
- 所有API调用错误都会记录到控制台
- 包含详细的错误信息和请求参数

### 3. 性能监控
- 请求响应时间监控
- 缓存命中率统计
- 错误率统计

## 注意事项

1. **依赖管理**: 确保安装了所有必要的Python包
2. **网络配置**: 确保Windows应用能够访问backend服务
3. **数据一致性**: API返回的数据格式与原有数据格式保持一致
4. **错误恢复**: 系统具备从API故障中恢复的能力
5. **安全性**: 考虑添加API认证和授权机制

## 故障排除

### 1. API连接失败
- 检查backend服务是否正常运行
- 检查网络连接和防火墙设置
- 检查API URL配置是否正确

### 2. 数据获取失败
- 检查股票代码格式是否正确
- 检查日期格式是否为YYYY-MM-DD
- 查看控制台错误日志

### 3. 性能问题
- 检查网络延迟
- 调整超时和重试配置
- 启用缓存机制 