[tool.poetry]
name = "ai-hedge-fund-gui"
version = "0.1.0"
description = "AI量化交易系统GUI"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src" }
]

[tool.poetry.dependencies]
python = "^3.11"
PyQt6 = "^6.6.1"
yfinance = "^0.2.36"
akshare = "^1.12.0"
ccxt = "^4.1.0"

[tool.poetry.group.dev.dependencies]
black = { version = "^24.1.1", extras = ["d"] }
mypy = "^1.8.0"
pytest = "^8.0.0"
pytest-cov = "^4.1.0"
flake8 = "^6.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src" 