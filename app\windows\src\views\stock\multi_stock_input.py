import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject
from typing import Dict, List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from models.market_manager import MarketManager
from styles import get_style

class MultiStockInputView(QWidget):
    """多选股票输入视图"""
    stocks_changed = pyqtSignal(list)  # 当股票列表改变时发出信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.market_list = {}  # 市场列表
        self.stock_data = {}   # 股票数据
        self.selected_stocks = []
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 市场选择
        market_layout = QHBoxLayout()
        market_label = QLabel("市场:")
        market_label.setStyleSheet(get_style("label"))
        self.market_combo = QComboBox()
        self.market_combo.setStyleSheet(get_style("combobox"))
        # 不在这里填充市场列表
        self.market_combo.currentIndexChanged.connect(self._on_market_changed)
        market_layout.addWidget(market_label)
        market_layout.addWidget(self.market_combo)
        market_layout.addStretch()

        # 股票选择
        stock_layout = QHBoxLayout()
        stock_label = QLabel("股票:")
        stock_label.setStyleSheet(get_style("label"))
        self.stock_combo = QComboBox()
        self.stock_combo.setStyleSheet(get_style("combobox"))
        self.stock_combo.setEditable(True)
        self.stock_combo.setMinimumWidth(300)
        self.stock_combo.lineEdit().setPlaceholderText("输入股票代码或名称")
        self.stock_combo.lineEdit().textEdited.connect(self._on_text_edited)
        self.stock_combo.currentIndexChanged.connect(self._on_stock_selected)
        stock_layout.addWidget(stock_label)
        stock_layout.addWidget(self.stock_combo)
        
        # 添加按钮
        add_btn = QPushButton("添加")
        add_btn.setStyleSheet(get_style("button_primary"))
        add_btn.clicked.connect(self._add_stock)
        stock_layout.addWidget(add_btn)
        
        stock_layout.addStretch()

        layout.addLayout(market_layout)
        layout.addLayout(stock_layout)

        # 已选股票列表
        selected_label = QLabel("已选股票:")
        selected_label.setStyleSheet(get_style("label"))
        layout.addWidget(selected_label)
        
        self.selected_list = QListWidget()
        self.selected_list.setMaximumHeight(120)
        self.selected_list.setStyleSheet(get_style("list_widget"))
        layout.addWidget(self.selected_list)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        clear_btn = QPushButton("清空")
        clear_btn.setStyleSheet(get_style("button_secondary"))
        clear_btn.clicked.connect(self._clear_selected)
        btn_layout.addWidget(clear_btn)
        
        remove_btn = QPushButton("删除选中")
        remove_btn.setStyleSheet(get_style("button_secondary"))
        remove_btn.clicked.connect(self._remove_selected)
        btn_layout.addWidget(remove_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)

    def set_market_list(self, market_list: dict):
        """由主控制器注入市场列表"""
        self.market_list = market_list or {}
        self.market_combo.blockSignals(True)
        self.market_combo.clear()
        for code, name in self.market_list.items():
            self.market_combo.addItem(name, code)
        self.market_combo.setCurrentIndex(-1)
        self.market_combo.blockSignals(False)

    def update_stock_data(self, stock_data: dict):
        """由主控制器注入股票数据"""
        self.stock_data = stock_data or {}
        self._update_stock_list()

    def _on_market_changed(self, index: int):
        self._update_stock_list()
        self._update_selected_list()

    def _update_stock_list(self):
        if not self.isVisible():
            return
        self.stock_combo.blockSignals(True)
        self.stock_combo.clear()
        market = self.market_combo.currentData()
        if market and market in self.stock_data:
            stocks = self.stock_data[market]
            for code, name in stocks.items():
                self.stock_combo.addItem(f"{name} ({code})", code)
        self.stock_combo.setCurrentIndex(-1)
        self.stock_combo.lineEdit().clear()
        self.stock_combo.blockSignals(False)

    def _on_text_edited(self, text: str):
        if not self.isVisible():
            return
        self.stock_combo.blockSignals(True)
        self.stock_combo.clear()
        market = self.market_combo.currentData()
        if market and market in self.stock_data:
            stocks = self.stock_data[market]
            if not text:
                for code, name in stocks.items():
                    self.stock_combo.addItem(f"{name} ({code})", code)
            else:
                for code, name in stocks.items():
                    if text.lower() in code.lower() or text.lower() in name.lower():
                        self.stock_combo.addItem(f"{name} ({code})", code)
        self.stock_combo.setCurrentIndex(-1)
        self.stock_combo.lineEdit().setText(text)
        self.stock_combo.blockSignals(False)
        self.stock_combo.showPopup()

    def _on_stock_selected(self, index: int):
        # 多选模式下，选择不自动添加，需要点击添加按钮
        pass

    def _add_stock(self):
        if self.stock_combo.currentIndex() >= 0:
            stock_code = self.stock_combo.currentData()
            if stock_code and stock_code not in self.selected_stocks:
                self.selected_stocks.append(stock_code)
                self._update_selected_list()
                self.stocks_changed.emit(self.selected_stocks)
                self.stock_combo.setCurrentIndex(-1)
                self.stock_combo.lineEdit().clear()

    def _update_selected_list(self):
        self.selected_list.clear()
        market = self.market_combo.currentData()
        if market and market in self.stock_data:
            stocks = self.stock_data[market]
            for stock_code in self.selected_stocks:
                stock_name = stocks.get(stock_code, stock_code)
                item = QListWidgetItem(f"{stock_name} ({stock_code})")
                item.setData(Qt.ItemDataRole.UserRole, stock_code)
                self.selected_list.addItem(item)

    def _clear_selected(self):
        self.selected_stocks.clear()
        self.selected_list.clear()
        self.stocks_changed.emit(self.selected_stocks)

    def _remove_selected(self):
        current_item = self.selected_list.currentItem()
        if current_item:
            stock_code = current_item.data(Qt.ItemDataRole.UserRole)
            if stock_code in self.selected_stocks:
                self.selected_stocks.remove(stock_code)
                self._update_selected_list()
                self.stocks_changed.emit(self.selected_stocks)

    def get_stocks(self) -> List[str]:
        return self.selected_stocks.copy()
    
    def get_market(self) -> str:
        return self.market_combo.currentData() or ""
    
    def refresh_market_data(self):
        """刷新当前市场的股票数据"""
        self._update_stock_list()
        self._update_selected_list()

    def refresh_styles(self):
        """刷新样式"""
        # 更新标签样式
        for child in self.findChildren(QLabel):
            child.setStyleSheet(get_style("label"))
            
        # 更新下拉框样式
        self.market_combo.setStyleSheet(get_style("combobox"))
        self.stock_combo.setStyleSheet(get_style("combobox"))
        
        # 更新列表样式
        self.selected_list.setStyleSheet(get_style("list_widget"))
        
    def clear(self):
        """清空选择"""
        self._clear_selected() 