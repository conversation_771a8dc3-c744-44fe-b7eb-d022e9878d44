import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QLabel, QTableWidget, QTableWidgetItem, QPushButton,
    QTextEdit, QScrollArea, QFrame, QSplitter, QProgressBar,
    QLineEdit, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QIcon
from typing import Dict, List, Any, Optional
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style

# 尝试导入matplotlib，如果失败则使用占位符
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import Figure<PERSON>anvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符类
    class Figure:
        def __init__(self, figsize=None):
            pass
        def clear(self):
            pass
        def add_subplot(self, *args, **kwargs):
            return None
    
    class FigureCanvas(QWidget):
        def __init__(self, figure):
            super().__init__()
            self.figure = figure

class ResultsView(QWidget):
    """分析结果显示视图"""

    # 信号定义
    chat_message_sent = pyqtSignal(str)  # 发送聊天消息信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_analysis_data = {}  # 存储当前分析数据
        self.selected_agent = None  # 当前选中的AI顾问
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        # 创建标题
        title = QLabel("分析结果")
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)

        # 创建标签页（左侧标签）
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.West)
        self.tab_widget.setStyleSheet(get_style("tab"))

        # 创建两个主要标签页
        self._create_analysis_results_tab()  # 分析结果标签页
        self._create_deep_conversation_tab()  # 深度对话标签页

        layout.addWidget(self.tab_widget)

        # 创建操作按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.export_btn = QPushButton("导出结果")
        self.export_btn.setStyleSheet(get_style("button_secondary"))
        self.export_btn.clicked.connect(self._on_export)
        button_layout.addWidget(self.export_btn)

        layout.addLayout(button_layout)

    def _create_analysis_results_tab(self):
        """创建分析结果标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(get_style("scroll_area"))

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)

        # 分析概览部分
        self._create_analysis_overview_section(content_layout)

        # AI顾问分析结果部分
        self._create_agent_analysis_section(content_layout)

        # 技术指标部分
        self._create_technical_indicators_section(content_layout)

        # 投资建议部分
        self._create_investment_recommendation_section(content_layout)

        content_layout.addStretch()
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(tab, "分析结果")

    def _create_deep_conversation_tab(self):
        """创建深度对话标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)

        # 对话历史区域
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setStyleSheet(get_style("text_edit"))
        self.chat_history.setPlaceholderText("与AI投资顾问的对话将显示在这里...")
        layout.addWidget(self.chat_history)

        # 输入区域
        input_layout = QHBoxLayout()

        self.chat_input = QLineEdit()
        self.chat_input.setStyleSheet(get_style("input"))
        self.chat_input.setPlaceholderText("输入您的问题...")
        self.chat_input.returnPressed.connect(self._send_chat_message)
        input_layout.addWidget(self.chat_input)

        self.chat_send_btn = QPushButton("发送")
        self.chat_send_btn.setStyleSheet(get_style("button_primary"))
        self.chat_send_btn.clicked.connect(self._send_chat_message)
        input_layout.addWidget(self.chat_send_btn)

        layout.addLayout(input_layout)

        self.tab_widget.addTab(tab, "深度对话")

    def _create_analysis_overview_section(self, parent_layout):
        """创建分析概览部分"""
        # 概览标题
        overview_title = QLabel("📊 分析概览")
        overview_title.setStyleSheet(get_style("subtitle"))
        parent_layout.addWidget(overview_title)

        # 概览内容
        self.overview_text = QTextEdit()
        self.overview_text.setReadOnly(True)
        self.overview_text.setMaximumHeight(120)
        self.overview_text.setStyleSheet(get_style("text_edit"))
        self.overview_text.setPlaceholderText("分析概览将在这里显示...")
        parent_layout.addWidget(self.overview_text)

    def _create_agent_analysis_section(self, parent_layout):
        """创建AI顾问分析结果部分"""
        # AI顾问分析标题
        agent_title = QLabel("🤖 AI投资顾问分析")
        agent_title.setStyleSheet(get_style("subtitle"))
        parent_layout.addWidget(agent_title)

        # AI顾问分析内容
        self.agent_analysis_text = QTextEdit()
        self.agent_analysis_text.setReadOnly(True)
        self.agent_analysis_text.setMinimumHeight(200)
        self.agent_analysis_text.setStyleSheet(get_style("text_edit"))
        self.agent_analysis_text.setPlaceholderText("AI投资顾问的分析结果将在这里显示...")
        parent_layout.addWidget(self.agent_analysis_text)

    def _create_technical_indicators_section(self, parent_layout):
        """创建技术指标部分"""
        # 技术指标标题
        tech_title = QLabel("📈 技术指标")
        tech_title.setStyleSheet(get_style("subtitle"))
        parent_layout.addWidget(tech_title)

        # 技术指标内容
        self.technical_text = QTextEdit()
        self.technical_text.setReadOnly(True)
        self.technical_text.setMaximumHeight(150)
        self.technical_text.setStyleSheet(get_style("text_edit"))
        self.technical_text.setPlaceholderText("技术指标分析将在这里显示...")
        parent_layout.addWidget(self.technical_text)

    def _create_investment_recommendation_section(self, parent_layout):
        """创建投资建议部分"""
        # 投资建议标题
        rec_title = QLabel("💡 投资建议")
        rec_title.setStyleSheet(get_style("subtitle"))
        parent_layout.addWidget(rec_title)

        # 投资建议内容
        self.recommendation_text = QTextEdit()
        self.recommendation_text.setReadOnly(True)
        self.recommendation_text.setMaximumHeight(120)
        self.recommendation_text.setStyleSheet(get_style("text_edit"))
        self.recommendation_text.setPlaceholderText("投资建议将在这里显示...")
        parent_layout.addWidget(self.recommendation_text)

    def _send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.text().strip()
        if not message:
            return

        # 清空输入框
        self.chat_input.clear()

        # 在聊天历史中显示用户消息
        self._add_chat_message("用户", message)

        # 发出信号
        self.chat_message_sent.emit(message)

    def _add_chat_message(self, sender: str, message: str):
        """添加聊天消息到历史记录"""
        current_text = self.chat_history.toPlainText()
        if current_text:
            current_text += "\n\n"

        new_message = f"【{sender}】\n{message}"
        self.chat_history.setPlainText(current_text + new_message)

        # 滚动到底部
        cursor = self.chat_history.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.chat_history.setTextCursor(cursor)

    def set_selected_agent(self, agent_name: str):
        """设置选中的AI顾问"""
        self.selected_agent = agent_name

        # 更新深度对话标签页的标题
        if hasattr(self, 'tab_widget'):
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "深度对话":
                    self.tab_widget.setTabText(i, f"与 {agent_name} 对话")
                    break

    def update_analysis_results(self, results: Dict[str, Any]):
        """更新分析结果"""
        self.current_analysis_data = results

        # 更新概览
        if "overview" in results:
            overview_text = self._format_overview(results["overview"])
            self.overview_text.setPlainText(overview_text)

        # 更新AI顾问分析
        if "agent_analysis" in results:
            agent_text = self._format_agent_analysis(results["agent_analysis"])
            self.agent_analysis_text.setPlainText(agent_text)

        # 更新技术指标
        if "technical_indicators" in results:
            tech_text = self._format_technical_indicators(results["technical_indicators"])
            self.technical_text.setPlainText(tech_text)

        # 更新投资建议
        if "recommendations" in results:
            rec_text = self._format_recommendations(results["recommendations"])
            self.recommendation_text.setPlainText(rec_text)

    def _format_overview(self, overview_data: Dict[str, Any]) -> str:
        """格式化概览数据"""
        if isinstance(overview_data, dict):
            lines = []
            for key, value in overview_data.items():
                lines.append(f"{key}: {value}")
            return "\n".join(lines)
        return str(overview_data)

    def _format_agent_analysis(self, agent_data: Dict[str, Any]) -> str:
        """格式化AI顾问分析数据"""
        if isinstance(agent_data, dict):
            lines = []
            for agent, analysis in agent_data.items():
                lines.append(f"【{agent}】")
                if isinstance(analysis, dict):
                    for key, value in analysis.items():
                        lines.append(f"  {key}: {value}")
                else:
                    lines.append(f"  {analysis}")
                lines.append("")
            return "\n".join(lines)
        return str(agent_data)

    def _format_technical_indicators(self, tech_data: Dict[str, Any]) -> str:
        """格式化技术指标数据"""
        if isinstance(tech_data, dict):
            lines = []
            for indicator, value in tech_data.items():
                lines.append(f"{indicator}: {value}")
            return "\n".join(lines)
        return str(tech_data)

    def _format_recommendations(self, rec_data: Dict[str, Any]) -> str:
        """格式化投资建议数据"""
        if isinstance(rec_data, dict):
            lines = []
            for key, value in rec_data.items():
                lines.append(f"{key}: {value}")
            return "\n".join(lines)
        return str(rec_data)

    def add_agent_response(self, response: str):
        """添加AI顾问的回复到聊天历史"""
        agent_name = self.selected_agent or "AI顾问"
        self._add_chat_message(agent_name, response)

    def clear_chat_history(self):
        """清空聊天历史"""
        self.chat_history.clear()

    def clear_results(self):
        """清空所有结果"""
        self.current_analysis_data = {}

        # 清空各个文本区域
        if hasattr(self, 'overview_text'):
            self.overview_text.clear()
        if hasattr(self, 'agent_analysis_text'):
            self.agent_analysis_text.clear()
        if hasattr(self, 'technical_text'):
            self.technical_text.clear()
        if hasattr(self, 'recommendation_text'):
            self.recommendation_text.clear()

        # 清空聊天历史
        self.clear_chat_history()

    def show_loading_state(self):
        """显示加载状态"""
        loading_text = "正在分析中，请稍候..."

        if hasattr(self, 'overview_text'):
            self.overview_text.setPlainText(loading_text)
        if hasattr(self, 'agent_analysis_text'):
            self.agent_analysis_text.setPlainText(loading_text)
        if hasattr(self, 'technical_text'):
            self.technical_text.setPlainText(loading_text)
        if hasattr(self, 'recommendation_text'):
            self.recommendation_text.setPlainText(loading_text)
        
    def _on_export(self):
        """导出结果处理"""
        if not self.current_analysis_data:
            QMessageBox.warning(self, "导出结果", "没有可导出的分析结果")
            return

        # TODO: 实现具体的导出功能（如保存为PDF、Excel等）
        QMessageBox.information(self, "导出结果", "导出功能正在开发中...")

    def refresh_styles(self):
        """刷新样式"""
        # 刷新标题样式
        for child in self.findChildren(QLabel):
            text = child.text()
            if text == "分析结果":
                child.setStyleSheet(get_style("title"))
            elif text.startswith("📊") or text.startswith("🤖") or text.startswith("📈") or text.startswith("💡"):
                child.setStyleSheet(get_style("subtitle"))
            else:
                child.setStyleSheet(get_style("label"))

        # 刷新按钮样式
        self.export_btn.setStyleSheet(get_style("button_secondary"))
        if hasattr(self, 'chat_send_btn'):
            self.chat_send_btn.setStyleSheet(get_style("button_primary"))

        # 刷新标签页样式
        self.tab_widget.setStyleSheet(get_style("tab"))

        # 刷新文本编辑器样式
        for child in self.findChildren(QTextEdit):
            child.setStyleSheet(get_style("text_edit"))

        # 刷新输入框样式
        for child in self.findChildren(QLineEdit):
            child.setStyleSheet(get_style("input"))