"""
API配置文件
管理API相关的配置信息
"""

import os
from typing import Dict, Any


class APIConfig:
    """API配置类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "base_url": "http://localhost:8000",
        "timeout": 30,
        "retry_count": 3,
        "retry_delay": 1,
        "enable_cache": True,
        "cache_duration": 3600,  # 1小时
    }
    
    def __init__(self, config_file: str = None):
        """
        初始化API配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config = self.DEFAULT_CONFIG.copy()
        
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
        
        # 从环境变量加载配置
        self.load_from_env()
    
    def load_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                self.config.update(file_config)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
    
    def load_from_env(self):
        """从环境变量加载配置"""
        # API基础URL
        if api_url := os.environ.get("AI_HEDGE_FUND_API_URL"):
            self.config["base_url"] = api_url
        
        # 超时时间
        if timeout := os.environ.get("AI_HEDGE_FUND_API_TIMEOUT"):
            try:
                self.config["timeout"] = int(timeout)
            except ValueError:
                pass
        
        # 重试次数
        if retry_count := os.environ.get("AI_HEDGE_FUND_API_RETRY_COUNT"):
            try:
                self.config["retry_count"] = int(retry_count)
            except ValueError:
                pass
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value
    
    def get_base_url(self) -> str:
        """获取API基础URL"""
        return self.config["base_url"]
    
    def get_timeout(self) -> int:
        """获取超时时间"""
        return self.config["timeout"]
    
    def get_retry_count(self) -> int:
        """获取重试次数"""
        return self.config["retry_count"]
    
    def get_retry_delay(self) -> int:
        """获取重试延迟"""
        return self.config["retry_delay"]
    
    def is_cache_enabled(self) -> bool:
        """是否启用缓存"""
        return self.config["enable_cache"]
    
    def get_cache_duration(self) -> int:
        """获取缓存持续时间"""
        return self.config["cache_duration"]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.config.copy()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")


# 全局配置实例
api_config = APIConfig() 