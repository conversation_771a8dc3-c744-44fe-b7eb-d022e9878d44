from typing import List, Dict, Optional
from datetime import datetime
import os
import requests
import ccxt
from .base import MarketProvider, MarketData, MarketInfo

class CryptoMarketProvider(MarketProvider):
    """加密货币市场数据提供者"""
    
    def __init__(self):
        # 初始化ccxt交易所接口
        self.exchange = ccxt.binance({
            'apiKey': os.environ.get('BINANCE_API_KEY'),
            'secret': os.environ.get('BINANCE_SECRET_KEY'),
            'enableRateLimit': True
        })
        
    def get_market_info(self) -> MarketInfo:
        return MarketInfo(
            name="Cryptocurrency Market",
            code="CRYPTO",
            currency="USDT",  # 默认使用USDT作为计价货币
            timezone="UTC",
            trading_hours={
                "24h": ["00:00", "23:59"]
            },
            trading_days=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        )
    
    def get_price_data(self, symbol: str, start_date: str, end_date: str) -> List[MarketData]:
        try:
            # 转换日期为毫秒时间戳
            start_ts = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
            end_ts = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
            
            # 获取K线数据
            ohlcv = self.exchange.fetch_ohlcv(
                symbol,
                timeframe='1d',
                since=start_ts,
                limit=1000
            )
            
            return [
                MarketData(
                    open=float(candle[1]),
                    close=float(candle[4]),
                    high=float(candle[2]),
                    low=float(candle[3]),
                    volume=float(candle[5]),
                    time=datetime.fromtimestamp(candle[0]/1000).strftime("%Y-%m-%d"),
                    currency="USDT",
                    exchange="Binance"
                )
                for candle in ohlcv
            ]
        except Exception as e:
            print(f"Error fetching crypto data: {e}")
            return []
    
    def get_financial_metrics(self, symbol: str, end_date: str) -> Dict:
        try:
            # 获取代币信息
            ticker = self.exchange.fetch_ticker(symbol)
            return {
                "market_cap": ticker.get("quoteVolume"),
                "volume_24h": ticker.get("baseVolume"),
                "price": ticker.get("last"),
                "high_24h": ticker.get("high"),
                "low_24h": ticker.get("low"),
                "change_24h": ticker.get("percentage"),
            }
        except Exception as e:
            print(f"Error fetching financial metrics: {e}")
            return {}
    
    def get_company_info(self, symbol: str) -> Dict:
        try:
            # 获取代币信息
            ticker = self.exchange.fetch_ticker(symbol)
            return {
                "symbol": symbol,
                "name": symbol.split("/")[0],
                "price": ticker.get("last"),
                "volume_24h": ticker.get("baseVolume"),
                "change_24h": ticker.get("percentage"),
            }
        except Exception as e:
            print(f"Error fetching company info: {e}")
            return {}
    
    def get_news(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        # 加密货币新闻通常通过第三方API获取
        # 这里返回空列表，实际使用时可以集成新闻API
        return []
    
    def get_insider_trades(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        # 加密货币没有内部交易的概念
        # 这里返回空列表
        return []
    
    def validate_symbol(self, symbol: str) -> bool:
        try:
            # 验证交易对是否存在
            self.exchange.load_markets()
            return symbol in self.exchange.markets
        except Exception as e:
            print(f"Error validating symbol: {e}")
            return False
    
    def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        # 加密货币市场24/7交易
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        days = []
        current = start
        while current <= end:
            days.append(current.strftime("%Y-%m-%d"))
            current = datetime.fromtimestamp(current.timestamp() + 86400)
        return days 