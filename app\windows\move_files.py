#!/usr/bin/env python3
"""
文件移动脚本 - 按功能模块重新组织文件
"""

import os
import shutil

def move_file(src, dst):
    """移动文件"""
    if os.path.exists(src):
        # 确保目标目录存在
        os.makedirs(os.path.dirname(dst), exist_ok=True)
        shutil.move(src, dst)
        print(f"移动: {src} -> {dst}")
    else:
        print(f"文件不存在: {src}")

def main():
    """主函数"""
    base_dir = "src/views"
    
    # 移动UI相关文件
    ui_files = [
        ("menu_bar.py", "ui/menu_bar.py"),
        ("toolbar.py", "ui/toolbar.py"), 
        ("navigation_tree.py", "ui/navigation_tree.py"),
        ("status_bar.py", "ui/status_bar.py"),
        ("loading_overlay.py", "ui/loading_overlay.py")
    ]
    
    # 移动分析相关文件
    analysis_files = [
        ("stock_input.py", "analysis/stock_input.py"),
        ("date_range.py", "analysis/date_range.py"),
        ("results_view.py", "analysis/results_view.py"),
        ("agent_selector.py", "analysis/agent_selector.py"),
        ("single_stock_single_agent.py", "analysis/single_stock_single_agent.py"),
        ("stock/single_stock_multi_agent.py", "analysis/single_stock_multi_agent.py")
    ]
    
    # 移动设置相关文件
    settings_files = [
        ("theme_switcher.py", "settings/theme_switcher.py"),
        ("llm_config.py", "settings/llm_config.py")
    ]
    
    # 移动通用文件
    common_files = [
        ("page_manager.py", "common/page_manager.py")
    ]
    
    # 执行移动
    for src, dst in ui_files + analysis_files + settings_files + common_files:
        src_path = os.path.join(base_dir, src)
        dst_path = os.path.join(base_dir, dst)
        move_file(src_path, dst_path)

if __name__ == "__main__":
    main() 