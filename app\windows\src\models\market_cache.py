from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class MarketCache:
    """市场数据缓存"""
    
    def __init__(self):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.expiry_times: Dict[str, datetime] = {}
        self.cache_duration = timedelta(hours=1)  # 缓存1小时
    
    def _get_cache_key(self, market: str, symbol: str, start_date: str, end_date: str) -> str:
        """生成缓存键"""
        return f"{market}_{symbol}_{start_date}_{end_date}"
    
    def get_data(self, market: str, symbol: str, start_date: str, end_date: str) -> Optional[Dict[str, Any]]:
        """获取缓存的数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Optional[Dict[str, Any]]: 缓存的数据，如果不存在或已过期则返回None
        """
        key = self._get_cache_key(market, symbol, start_date, end_date)
        
        # 检查缓存是否存在且未过期
        if key in self.cache and key in self.expiry_times:
            if datetime.now() < self.expiry_times[key]:
                return self.cache[key]
            else:
                # 缓存已过期，删除
                del self.cache[key]
                del self.expiry_times[key]
        
        return None
    
    def set_data(self, market: str, symbol: str, start_date: str, end_date: str, data: Dict[str, Any]):
        """设置缓存数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            data: 要缓存的数据
        """
        key = self._get_cache_key(market, symbol, start_date, end_date)
        self.cache[key] = data
        self.expiry_times[key] = datetime.now() + self.cache_duration
    
    def clear_expired(self):
        """清除过期的缓存"""
        now = datetime.now()
        expired_keys = [
            key for key, expiry in self.expiry_times.items()
            if now >= expiry
        ]
        for key in expired_keys:
            del self.cache[key]
            del self.expiry_times[key]
    
    def clear_all(self):
        """清除所有缓存"""
        self.cache.clear()
        self.expiry_times.clear() 