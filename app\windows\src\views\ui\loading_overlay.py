from PyQt6.QtWidgets import QFrame, QVBoxLayout, QLabel, QProgressBar
from PyQt6.QtCore import Qt, QTimer
from styles import get_style


class LoadingOverlay(QFrame):
    """加载遮罩层"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self._animation_timer = QTimer()
        self._animation_timer.timeout.connect(self._animate_dots)
        self._dots_count = 0
        self._base_status_text = ""

    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(get_style("loading_overlay"))

        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(15)

        # 加载文本
        self.loading_label = QLabel("正在初始化系统...")
        self.loading_label.setStyleSheet(get_style("loading_label"))
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.loading_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet(get_style("progress_bar"))
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.progress_bar.setFixedSize(300, 20)
        layout.addWidget(self.progress_bar)

        # 状态文本
        self.status_label = QLabel("请稍候...")
        self.status_label.setStyleSheet(get_style("loading_status"))
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
    def update_status(self, message: str):
        """更新状态信息"""
        self._base_status_text = message
        self._start_animation()

    def show_message(self, message: str):
        """显示加载消息"""
        self.loading_label.setText(message)

    def _start_animation(self):
        """开始动画效果"""
        if not self._animation_timer.isActive():
            self._animation_timer.start(500)  # 每500ms更新一次

    def _stop_animation(self):
        """停止动画效果"""
        if self._animation_timer.isActive():
            self._animation_timer.stop()

    def _animate_dots(self):
        """动画效果：添加动态点"""
        self._dots_count = (self._dots_count + 1) % 4
        dots = "." * self._dots_count
        self.status_label.setText(f"{self._base_status_text}{dots}")

    def refresh_styles(self):
        """刷新样式"""
        self.setStyleSheet(get_style("loading_overlay"))
        self.loading_label.setStyleSheet(get_style("loading_label"))
        self.status_label.setStyleSheet(get_style("loading_status"))
        self.progress_bar.setStyleSheet(get_style("progress_bar"))

    def show(self):
        super().show()
        self.raise_()

    def hide(self):
        """隐藏遮罩层"""
        self._stop_animation()
        super().hide()