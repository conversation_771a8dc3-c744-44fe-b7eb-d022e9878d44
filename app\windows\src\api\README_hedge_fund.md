# 对冲基金API客户端 (HedgeFundClient)

## 概述

`HedgeFundClient` 是一个专门用于与后端对冲基金API通信的客户端类，继承自基础 `APIClient` 类。它提供了完整的对冲基金分析功能，包括获取分析师代理、语言模型、执行分析等。

## 功能特性

- ✅ 获取可用的分析师代理列表
- ✅ 获取可用的语言模型列表
- ✅ 同步执行对冲基金分析
- ✅ 流式执行对冲基金分析（支持实时进度更新）
- ✅ 请求参数验证
- ✅ 默认分析时间段计算
- ✅ 错误处理和重试机制
- ✅ 支持自定义代理模型配置

## 快速开始

### 1. 基本使用

```python
from api import HedgeFundClient

# 创建客户端实例
client = HedgeFundClient("http://localhost:8000")

# 检查API连接
if client.health_check():
    print("API服务器连接成功")
```

### 2. 获取可用资源

```python
# 获取分析师代理列表
agents = client.get_agents()
print(f"可用代理: {agents['agents']}")

# 获取语言模型列表
models = client.get_language_models()
print(f"可用模型: {models['models']}")
```

### 3. 执行分析

#### 同步分析

```python
result = client.run_hedge_fund_sync(
    tickers=["AAPL", "GOOGL", "MSFT"],
    selected_agents=["warren_buffett", "peter_lynch"],
    model_name="gpt-4o",
    model_provider="openai",
    initial_cash=100000.0
)

if "error" not in result:
    print("分析完成!")
    print(f"决策: {result['decisions']}")
    print(f"分析师信号: {result['analyst_signals']}")
```

#### 流式分析

```python
def progress_callback(event):
    if event["type"] == "progress":
        data = event["data"]
        print(f"{data['agent']} 正在分析 {data['ticker']}: {data['status']}")

def complete_callback(result):
    print("分析完成!")
    print(f"决策数量: {len(result['decisions'])}")

result = client.run_hedge_fund_streaming(
    tickers=["AAPL", "GOOGL"],
    selected_agents=["warren_buffett", "peter_lynch"],
    progress_callback=progress_callback,
    complete_callback=complete_callback
)
```

## API 参考

### 构造函数

```python
HedgeFundClient(base_url: str = "http://localhost:8000")
```

**参数:**
- `base_url`: API服务器的基础URL，默认为 `http://localhost:8000`

### 方法

#### get_agents()

获取可用的分析师代理列表。

**返回:** `Dict[str, Any]` - 包含代理列表的字典

#### get_language_models()

获取可用的语言模型列表。

**返回:** `Dict[str, Any]` - 包含模型列表的字典

#### run_hedge_fund_sync()

同步执行对冲基金分析。

**参数:**
- `tickers: List[str]` - 股票代码列表
- `selected_agents: List[str]` - 选中的分析师代理列表
- `agent_models: Optional[List[Dict[str, Any]]]` - 代理模型配置列表
- `end_date: Optional[str]` - 结束日期 (YYYY-MM-DD)
- `start_date: Optional[str]` - 开始日期 (YYYY-MM-DD)
- `model_name: str` - 模型名称，默认 "gpt-4o"
- `model_provider: str` - 模型提供商，默认 "openai"
- `initial_cash: float` - 初始资金，默认 100000.0
- `margin_requirement: float` - 保证金要求，默认 0.0

**返回:** `Dict[str, Any]` - 分析结果

#### run_hedge_fund_streaming()

流式执行对冲基金分析。

**参数:**
- `tickers: List[str]` - 股票代码列表
- `selected_agents: List[str]` - 选中的分析师代理列表
- `agent_models: Optional[List[Dict[str, Any]]]` - 代理模型配置列表
- `end_date: Optional[str]` - 结束日期 (YYYY-MM-DD)
- `start_date: Optional[str]` - 开始日期 (YYYY-MM-DD)
- `model_name: str` - 模型名称，默认 "gpt-4o"
- `model_provider: str` - 模型提供商，默认 "openai"
- `initial_cash: float` - 初始资金，默认 100000.0
- `margin_requirement: float` - 保证金要求，默认 0.0
- `progress_callback: Optional[Callable]` - 进度回调函数
- `complete_callback: Optional[Callable]` - 完成回调函数
- `error_callback: Optional[Callable]` - 错误回调函数

**返回:** `Dict[str, Any]` - 最终分析结果

#### validate_hedge_fund_request()

验证对冲基金请求参数。

**参数:**
- `tickers: List[str]` - 股票代码列表
- `selected_agents: List[str]` - 选中的分析师代理列表
- `end_date: Optional[str]` - 结束日期
- `start_date: Optional[str]` - 开始日期

**返回:** `Dict[str, Any]` - 验证结果，包含 `is_valid` 和 `errors` 字段

#### get_default_analysis_period()

获取默认分析时间段。

**参数:**
- `end_date: Optional[str]` - 结束日期，如果为None则使用当前日期

**返回:** `Dict[str, str]` - 包含开始和结束日期的字典

## 使用示例

### 基本分析

```python
client = HedgeFundClient()

# 验证参数
validation = client.validate_hedge_fund_request(
    tickers=["AAPL", "GOOGL"],
    selected_agents=["warren_buffett"]
)

if validation["is_valid"]:
    result = client.run_hedge_fund_sync(
        tickers=["AAPL", "GOOGL"],
        selected_agents=["warren_buffett"],
        model_name="gpt-4o"
    )
    print(result)
```

### 高级配置

```python
# 自定义代理模型配置
agent_models = [
    {
        "agent_id": "warren_buffett",
        "model_name": "gpt-4o",
        "model_provider": "openai"
    },
    {
        "agent_id": "peter_lynch",
        "model_name": "claude-3-sonnet",
        "model_provider": "anthropic"
    }
]

result = client.run_hedge_fund_sync(
    tickers=["AAPL", "GOOGL", "MSFT"],
    selected_agents=["warren_buffett", "peter_lynch"],
    agent_models=agent_models,
    start_date="2024-01-01",
    end_date="2024-03-31",
    initial_cash=500000.0,
    margin_requirement=0.1
)
```

### 流式分析

```python
def on_progress(event):
    if event["type"] == "start":
        print("开始分析...")
    elif event["type"] == "progress":
        data = event["data"]
        print(f"{data['agent']}: {data['status']}")

def on_complete(result):
    print("分析完成!")
    print(f"决策: {result['decisions']}")

def on_error(error):
    print(f"错误: {error}")

client.run_hedge_fund_streaming(
    tickers=["AAPL", "GOOGL"],
    selected_agents=["warren_buffett", "peter_lynch"],
    progress_callback=on_progress,
    complete_callback=on_complete,
    error_callback=on_error
)
```

## 错误处理

客户端包含完善的错误处理机制：

1. **网络错误**: 自动重试机制，最多重试3次
2. **参数验证**: 内置参数验证功能
3. **API错误**: 捕获并返回详细的错误信息
4. **超时处理**: 设置合理的超时时间

## 注意事项

1. **API服务器**: 确保后端API服务器正在运行
2. **网络连接**: 检查网络连接和防火墙设置
3. **参数格式**: 日期格式必须为 `YYYY-MM-DD`
4. **股票代码**: 使用正确的股票代码格式
5. **代理名称**: 使用正确的分析师代理ID

## 依赖项

- `requests`: HTTP请求库
- `json`: JSON数据处理
- `datetime`: 日期时间处理
- `typing`: 类型注解

## 相关文件

- `client.py`: 基础API客户端
- `stock_data_client.py`: 股票数据API客户端
- `hedge_fund_example.py`: 使用示例 