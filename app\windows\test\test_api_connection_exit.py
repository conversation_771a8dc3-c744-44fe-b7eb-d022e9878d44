#!/usr/bin/env python3
"""
API连接检查退出测试脚本
测试修改后的启动时API检查逻辑，验证连接失败时程序退出
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from api.stock_data_client import StockDataClient
from models.initializer import StockDataInitializer


def test_api_connection_check():
    """测试API连接检查逻辑"""
    print("=" * 60)
    print("API连接检查逻辑测试")
    print("=" * 60)
    
    # 测试1: 使用错误的API URL
    print("\n1. 测试错误的API URL...")
    try:
        initializer = StockDataInitializer("http://invalid-url:9999")
        print("✓ 初始化器创建成功")
        
        # 模拟运行初始化
        print("  正在检查API连接...")
        is_healthy = initializer.api_client.health_check()
        print(f"  API健康检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            print("  ⚠ 意外：API连接正常")
        else:
            print("  ✓ API连接异常，程序应该退出")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 测试2: 使用正确的API URL（如果后端运行）
    print("\n2. 测试正确的API URL...")
    try:
        initializer = StockDataInitializer("http://localhost:8000")
        print("✓ 初始化器创建成功")
        
        # 模拟运行初始化
        print("  正在检查API连接...")
        is_healthy = initializer.api_client.health_check()
        print(f"  API健康检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            print("  ✓ API连接正常，程序应该继续运行")
        else:
            print("  ⚠ API连接异常，程序应该退出")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 连接检查逻辑测试完成")
    print("=" * 60)


def test_client_behavior():
    """测试客户端行为"""
    print("\n" + "=" * 60)
    print("客户端行为测试")
    print("=" * 60)
    
    # 测试1: 错误客户端
    print("\n1. 测试错误客户端...")
    try:
        client = StockDataClient("http://invalid-url:9999")
        
        # 测试健康检查
        is_healthy = client.health_check()
        print(f"  健康检查: {'正常' if is_healthy else '异常'}")
        
        # 测试获取市场列表（应该抛出异常）
        try:
            markets = client.get_available_markets()
            print(f"  市场列表: {len(markets)} 个市场")
            print("  ⚠ 意外：没有抛出异常")
        except Exception as e:
            print(f"  ✓ 正确抛出异常: {str(e)}")
        
    except Exception as e:
        print(f"✗ 错误客户端测试失败: {str(e)}")
    
    # 测试2: 正常客户端（如果后端运行）
    print("\n2. 测试正常客户端...")
    try:
        client = StockDataClient("http://localhost:8000")
        
        # 测试健康检查
        is_healthy = client.health_check()
        print(f"  健康检查: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            # 测试获取市场列表
            try:
                markets = client.get_available_markets()
                print(f"  市场列表: {len(markets)} 个市场")
            except Exception as e:
                print(f"  ✗ 获取市场列表失败: {str(e)}")
        else:
            print("  ⚠ API连接异常")
        
    except Exception as e:
        print(f"✗ 正常客户端测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 客户端行为测试完成")
    print("=" * 60)


def test_initialization_flow():
    """测试初始化流程"""
    print("\n" + "=" * 60)
    print("初始化流程测试")
    print("=" * 60)
    
    # 测试1: 错误URL的初始化流程
    print("\n1. 测试错误URL的初始化流程...")
    try:
        initializer = StockDataInitializer("http://invalid-url:9999")
        
        # 模拟运行初始化
        print("  开始初始化...")
        initializer.run()
        print("  ⚠ 意外：初始化没有抛出异常")
        
    except Exception as e:
        print(f"  ✓ 正确抛出异常: {str(e)}")
    
    # 测试2: 正确URL的初始化流程（如果后端运行）
    print("\n2. 测试正确URL的初始化流程...")
    try:
        initializer = StockDataInitializer("http://localhost:8000")
        
        # 模拟运行初始化
        print("  开始初始化...")
        initializer.run()
        print("  ✓ 初始化成功")
        
    except Exception as e:
        print(f"  ✗ 初始化失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 初始化流程测试完成")
    print("=" * 60)


def main():
    """主函数"""
    print("AI Hedge Fund - API连接检查退出测试")
    
    # 测试连接检查逻辑
    test_api_connection_check()
    
    # 测试客户端行为
    test_client_behavior()
    
    # 测试初始化流程
    test_initialization_flow()
    
    print("\n🎉 所有测试完成！")
    print("\n总结:")
    print("1. 启动时先检查API连接（/ping或/）")
    print("2. 如果无法连通，弹出错误提示")
    print("3. 用户确认后，程序直接退出")
    print("4. 不再使用默认数据进行后续操作")
    print("5. 只有API连接正常时，程序才继续运行")


if __name__ == "__main__":
    main() 