"""
股票数据API客户端
提供与后端股票数据API的通信接口
"""

from typing import Dict, List, Optional, Any
from .client import APIClient


class StockDataClient(APIClient):
    """股票数据API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化股票数据API客户端
        
        Args:
            base_url: API服务器的基础URL
        """
        super().__init__(base_url)
        self.base_endpoint = "/api/stock-data"
    
    def get_available_markets(self) -> Dict[str, str]:
        """
        获取可用的市场列表
        
        Returns:
            Dict[str, str]: 市场代码到市场名称的映射
        """
        response = self.get(f"{self.base_endpoint}/markets")
        return response
    
    def get_stock_list(self, market: str) -> Dict[str, str]:
        """
        获取指定市场的股票列表
        
        Args:
            market: 市场代码 (US, CN, CRYPTO)
            
        Returns:
            Dict[str, str]: 股票代码到股票名称的映射
        """
        response = self.get(f"{self.base_endpoint}/stocks/{market}")
        return response.get("stocks", {})
    
    def get_price_data(
        self, 
        market: str, 
        symbol: str, 
        start_date: str, 
        end_date: str
    ) -> List[Dict[str, Any]]:
        """
        获取股票价格数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            List[Dict[str, Any]]: 价格数据列表
        """
        try:
            params = {
                "start_date": start_date,
                "end_date": end_date
            }
            response = self.get(f"{self.base_endpoint}/price/{market}/{symbol}", params)
            return response.get("data", [])
        except Exception as e:
            print(f"获取价格数据失败: {str(e)}")
            return []
    
    def get_financial_metrics(
        self, 
        market: str, 
        symbol: str, 
        end_date: str
    ) -> Dict[str, Any]:
        """
        获取财务指标数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            Dict[str, Any]: 财务指标数据
        """
        try:
            params = {"end_date": end_date}
            response = self.get(f"{self.base_endpoint}/financial/{market}/{symbol}", params)
            return response.get("metrics", {})
        except Exception as e:
            print(f"获取财务指标失败: {str(e)}")
            return {}
    
    def get_company_info(self, market: str, symbol: str) -> Dict[str, Any]:
        """
        获取公司信息
        
        Args:
            market: 市场代码
            symbol: 股票代码
            
        Returns:
            Dict[str, Any]: 公司信息
        """
        try:
            response = self.get(f"{self.base_endpoint}/company/{market}/{symbol}")
            return response.get("info", {})
        except Exception as e:
            print(f"获取公司信息失败: {str(e)}")
            return {}
    
    def get_news(
        self, 
        market: str, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取新闻数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            List[Dict[str, Any]]: 新闻数据列表
        """
        try:
            params = {}
            if start_date:
                params["start_date"] = start_date
            if end_date:
                params["end_date"] = end_date
                
            response = self.get(f"{self.base_endpoint}/news/{market}/{symbol}", params)
            return response.get("news", [])
        except Exception as e:
            print(f"获取新闻数据失败: {str(e)}")
            return []
    
    def get_insider_trades(
        self, 
        market: str, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取内部交易数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            List[Dict[str, Any]]: 内部交易数据列表
        """
        try:
            params = {}
            if start_date:
                params["start_date"] = start_date
            if end_date:
                params["end_date"] = end_date
                
            response = self.get(f"{self.base_endpoint}/insider-trades/{market}/{symbol}", params)
            return response.get("trades", [])
        except Exception as e:
            print(f"获取内部交易数据失败: {str(e)}")
            return []
    
    def get_market_info(self, market: str) -> Dict[str, Any]:
        """
        获取市场信息
        
        Args:
            market: 市场代码
            
        Returns:
            Dict[str, Any]: 市场信息
        """
        try:
            response = self.get(f"{self.base_endpoint}/market-info/{market}")
            return response.get("info", {})
        except Exception as e:
            print(f"获取市场信息失败: {str(e)}")
            return {}
    
    def validate_symbol(self, market: str, symbol: str) -> bool:
        """
        验证股票代码是否有效
        
        Args:
            market: 市场代码
            symbol: 股票代码
            
        Returns:
            bool: 是否有效
        """
        response = self.get(f"{self.base_endpoint}/validate/{market}/{symbol}")
        return response.get("is_valid", False) 