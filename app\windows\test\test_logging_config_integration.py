#!/usr/bin/env python3
"""
测试日志配置与配置管理系统的集成
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from utils.config_manager import get_config_manager
    from api.logger_config import logger_config
    from api.client import APIClient

    def test_config_manager_integration():
        """测试ConfigManager集成"""
        print("=== 测试ConfigManager集成 ===")
        
        config_manager = get_config_manager()
        
        # 测试默认日志配置
        print("1. 测试默认日志配置:")
        logging_config = config_manager.get_logging_config()
        print(f"   默认配置: {logging_config}")
        
        # 测试设置日志配置
        print("\n2. 测试设置日志配置:")
        new_config = {
            "debug_enabled": False,
            "console_enabled": True,
            "file_enabled": False,
            "max_file_size_mb": 20,
            "max_files": 10,
            "cleanup_days": 14
        }
        
        success = config_manager.set_logging_config(new_config)
        print(f"   设置结果: {'成功' if success else '失败'}")
        
        # 验证配置是否保存
        saved_config = config_manager.get_logging_config()
        print(f"   保存的配置: {saved_config}")
        
        # 验证配置是否正确
        config_match = all(saved_config.get(k) == v for k, v in new_config.items())
        print(f"   配置匹配: {'✅' if config_match else '❌'}")

    def test_logger_config_integration():
        """测试LoggerConfig与ConfigManager的集成"""
        print("\n=== 测试LoggerConfig集成 ===")
        
        # 测试LoggerConfig是否使用ConfigManager
        print(f"1. LoggerConfig使用ConfigManager: {'✅' if logger_config.use_config_manager else '❌'}")
        
        # 测试配置读取
        print("\n2. 测试配置读取:")
        config = logger_config.get_config()
        print(f"   当前配置: {config}")
        
        # 测试配置更新
        print("\n3. 测试配置更新:")
        original_debug = config.get("debug_enabled", True)
        
        # 切换调试模式
        logger_config.enable_debug(not original_debug)
        new_config = logger_config.get_config()
        print(f"   更新后配置: {new_config}")
        
        # 验证更新是否生效
        debug_updated = new_config.get("debug_enabled") != original_debug
        print(f"   调试模式更新: {'✅' if debug_updated else '❌'}")
        
        # 恢复原始设置
        logger_config.enable_debug(original_debug)

    def test_unified_config_file():
        """测试统一配置文件"""
        print("\n=== 测试统一配置文件 ===")
        
        config_manager = get_config_manager()
        
        # 获取完整配置
        full_config = config_manager.get_config()
        print("1. 完整配置结构:")
        for section, content in full_config.items():
            print(f"   {section}: {type(content)} ({len(content) if isinstance(content, dict) else 'N/A'} 项)")
        
        # 验证日志配置是否在统一配置中
        has_logging_section = "logging" in full_config
        print(f"\n2. 包含日志配置: {'✅' if has_logging_section else '❌'}")
        
        if has_logging_section:
            logging_section = full_config["logging"]
            required_keys = ["debug_enabled", "console_enabled", "file_enabled", 
                           "max_file_size_mb", "max_files", "cleanup_days"]
            missing_keys = [key for key in required_keys if key not in logging_section]
            print(f"   日志配置完整性: {'✅' if not missing_keys else f'❌ 缺少: {missing_keys}'}")

    def test_api_client_with_unified_config():
        """测试API客户端使用统一配置"""
        print("\n=== 测试API客户端配置集成 ===")
        
        # 创建API客户端
        client = APIClient("http://localhost:8000", debug=True)
        
        # 测试日志功能
        print("1. 测试API客户端日志:")
        try:
            result = client.health_check()
            print(f"   健康检查: {'✅' if result else '❌'}")
        except Exception as e:
            print(f"   健康检查异常: {e}")
        
        # 测试配置变更对API客户端的影响
        print("\n2. 测试配置变更影响:")
        
        # 关闭调试模式
        logger_config.enable_debug(False)
        print("   已关闭调试模式")
        
        # 再次测试API调用
        try:
            client.health_check()
            print("   关闭调试后API调用: ✅")
        except Exception as e:
            print(f"   关闭调试后API调用异常: {e}")
        
        # 重新启用调试模式
        logger_config.enable_debug(True)
        print("   已重新启用调试模式")
        
        client.close()

    def test_config_persistence():
        """测试配置持久化"""
        print("\n=== 测试配置持久化 ===")
        
        config_manager = get_config_manager()
        
        # 获取配置文件路径
        config_file_path = config_manager.get_config_file_path()
        print(f"1. 配置文件路径: {config_file_path}")
        print(f"   文件存在: {'✅' if os.path.exists(config_file_path) else '❌'}")
        
        # 测试配置保存和加载
        print("\n2. 测试配置保存和加载:")
        
        # 保存测试配置
        test_config = {
            "debug_enabled": True,
            "console_enabled": False,
            "file_enabled": True,
            "max_file_size_mb": 15,
            "max_files": 7,
            "cleanup_days": 10
        }
        
        save_success = config_manager.set_logging_config(test_config)
        print(f"   保存测试配置: {'✅' if save_success else '❌'}")
        
        # 重新加载配置
        loaded_config = config_manager.get_logging_config()
        config_matches = all(loaded_config.get(k) == v for k, v in test_config.items())
        print(f"   配置加载匹配: {'✅' if config_matches else '❌'}")
        
        if not config_matches:
            print(f"   期望: {test_config}")
            print(f"   实际: {loaded_config}")

    def test_quick_actions():
        """测试快速操作"""
        print("\n=== 测试快速操作 ===")
        
        # 测试完整日志模式
        print("1. 测试完整日志模式:")
        logger_config.enable_full_logging()
        config = logger_config.get_config()
        full_logging_correct = (
            config.get("debug_enabled") and 
            config.get("console_enabled") and 
            config.get("file_enabled")
        )
        print(f"   完整日志模式: {'✅' if full_logging_correct else '❌'}")
        
        # 测试最小日志模式
        print("\n2. 测试最小日志模式:")
        logger_config.enable_minimal_logging()
        config = logger_config.get_config()
        minimal_logging_correct = (
            config.get("debug_enabled") and 
            config.get("console_enabled") and 
            not config.get("file_enabled")
        )
        print(f"   最小日志模式: {'✅' if minimal_logging_correct else '❌'}")
        
        # 测试关闭日志
        print("\n3. 测试关闭日志:")
        logger_config.disable_all_logging()
        config = logger_config.get_config()
        logging_disabled = (
            not config.get("debug_enabled") and 
            not config.get("console_enabled") and 
            not config.get("file_enabled")
        )
        print(f"   关闭日志: {'✅' if logging_disabled else '❌'}")
        
        # 恢复默认设置
        logger_config.enable_full_logging()

    def main():
        """主测试函数"""
        print("日志配置与配置管理系统集成测试")
        print("=" * 50)
        
        try:
            # 配置管理器集成测试
            test_config_manager_integration()
            
            # LoggerConfig集成测试
            test_logger_config_integration()
            
            # 统一配置文件测试
            test_unified_config_file()
            
            # API客户端配置集成测试
            test_api_client_with_unified_config()
            
            # 配置持久化测试
            test_config_persistence()
            
            # 快速操作测试
            test_quick_actions()
            
            print("\n" + "=" * 50)
            print("✅ 所有集成测试完成！")
            print("\n主要成果:")
            print("1. ✅ 日志配置已集成到ConfigManager")
            print("2. ✅ 统一配置文件管理")
            print("3. ✅ LoggerConfig自动使用ConfigManager")
            print("4. ✅ 配置持久化正常工作")
            print("5. ✅ API客户端配置集成成功")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已正确设置Python路径")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
