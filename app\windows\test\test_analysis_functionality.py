#!/usr/bin/env python3
"""
测试单股单顾问分析功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
    from PyQt6.QtCore import QTimer
    from views.stock.single_stock_single_agent import SingleStockSingleAgentPage
    
    class TestWindow(QMainWindow):
        """测试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("单股单顾问分析功能测试")
            self.setGeometry(100, 100, 1400, 900)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # 添加说明
            info_label = QLabel("测试单股单顾问分析功能")
            info_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
            layout.addWidget(info_label)
            
            # 创建单股单顾问页面
            self.single_stock_page = SingleStockSingleAgentPage()
            layout.addWidget(self.single_stock_page)
            
            # 添加测试按钮
            test_layout = QVBoxLayout()
            
            self.test_market_btn = QPushButton("测试设置市场列表")
            self.test_market_btn.clicked.connect(self.test_set_market_list)
            test_layout.addWidget(self.test_market_btn)
            
            self.test_stock_btn = QPushButton("测试设置股票列表")
            self.test_stock_btn.clicked.connect(self.test_set_stock_list)
            test_layout.addWidget(self.test_stock_btn)
            
            layout.addLayout(test_layout)
            
            # 延迟设置测试数据
            QTimer.singleShot(1000, self.setup_test_data)
        
        def setup_test_data(self):
            """设置测试数据"""
            self.test_set_market_list()
            QTimer.singleShot(500, self.test_set_stock_list)
        
        def test_set_market_list(self):
            """测试设置市场列表"""
            print("\n=== 测试设置市场列表 ===")
            market_list = {
                "SH": "上海证券交易所",
                "SZ": "深圳证券交易所",
                "HK": "香港交易所"
            }
            self.single_stock_page.set_market_list(market_list)
            print(f"已设置市场列表: {market_list}")
        
        def test_set_stock_list(self):
            """测试设置股票列表"""
            print("\n=== 测试设置股票列表 ===")
            stock_list = {
                "000001": "平安银行",
                "000002": "万科A",
                "000858": "五粮液",
                "600000": "浦发银行",
                "600036": "招商银行",
                "600519": "贵州茅台"
            }
            # 为上海市场设置股票列表
            self.single_stock_page.set_stock_list("SH", stock_list)
            print(f"已为上海市场设置股票列表: {stock_list}")
            print("检查第一个股票是否自动选中")

    def main():
        """主函数"""
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = TestWindow()
        window.show()
        
        print("单股单顾问分析功能测试已启动")
        print("修复验证:")
        print("1. AI顾问选择问题 - 验证是否正确识别默认选择")
        print("2. API调用问题 - 验证是否使用正确的agent key")
        print("3. 422错误修复 - 验证API参数格式")
        print("\n新功能测试:")
        print("1. 分析结果标签页 - 检查是否使用左侧标签布局")
        print("2. 深度对话标签页 - 检查聊天界面是否正常")
        print("3. 开始分析按钮 - 测试API集成和流式响应")
        print("4. 错误处理 - 验证各种错误情况的处理")
        print("5. 状态管理 - 检查分析过程中的状态变化")
        print("\n请在界面中进行交互测试...")
        print("注意观察:")
        print("- 市场、股票和AI顾问是否自动选择")
        print("- AI顾问下拉框显示的是display_name，但发送的是key")
        print("- 选择日期后开始分析按钮是否启用")
        print("- 点击开始分析后不再提示'请选择投资顾问'")
        print("- API调用不再返回422错误")
        print("- 分析结果是否在左侧标签页中正确显示")
        print("- 深度对话功能是否正常工作")
        print("- 控制台调试信息显示正确的参数")
        
        sys.exit(app.exec())

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt6和相关依赖")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
