# AI量化交易系统 - Windows应用启动指南

## 🚀 快速启动

### 方法1：使用新的启动脚本（推荐）
```bash
cd app/windows
python run.py
```

### 方法2：使用原有的启动脚本
```bash
cd app/windows
python run_main.py
```

### 方法3：直接运行主窗口
```bash
cd app/windows/src
python main_window.py
```

## 📋 启动前准备

### 1. 确保Python环境
- Python 3.8+ 
- 推荐使用Python 3.11

### 2. 安装依赖
```bash
cd app/windows
pip install -r requirements.txt
```

或者使用Poetry（如果已安装）：
```bash
cd app/windows
poetry install
poetry shell
```

### 3. 检查API服务器（可选）
如果需要连接后端API服务器：
```bash
# 在项目根目录启动后端服务器
cd ../../
python -m uvicorn src.main:app --reload --port 8000
```

## 🎯 启动流程说明

### 新的加载状态显示
应用启动时，您将看到以下详细的初始化状态：

1. **🚀 系统启动**
   - "正在启动系统..."
   - "正在检查API连接..."

2. **🔗 API连接验证**
   - "正在连接服务器..."
   - "API连接正常，正在初始化系统..."

3. **⚙️ 系统组件初始化**
   - "正在创建市场管理器..."
   - "正在准备数据初始化..."
   - "正在启动初始化任务..."

4. **📈 数据加载阶段**
   - "正在获取股票数据..." - 开始数据获取
   - "正在加载美股数据..." (显示具体数量)
   - "正在加载A股数据..." (显示具体数量)
   - "正在加载加密货币数据..." (显示具体数量)

5. **🔄 数据整理**
   - "正在整理数据..."
   - "初始化完成 (共X个标的)"

6. **✅ 启动完成**
   - "系统初始化完成"
   - "正在进入系统..."

### 动态效果
- 状态文本后会显示动态的点效果（...）
- 加载消息会根据当前阶段智能切换
- API连接状态实时显示在状态栏

## 🔧 故障排除

### 常见问题

#### 1. PyQt6导入错误
```
错误：无法导入PyQt6模块
```
**解决方案：**
```bash
pip install PyQt6
```

#### 2. API连接失败
如果看到"API连接失败"对话框：
- 检查后端服务器是否运行在 http://localhost:8000
- 检查网络连接
- 检查防火墙设置

#### 3. 依赖缺失
```
ModuleNotFoundError: No module named 'xxx'
```
**解决方案：**
```bash
pip install -r requirements.txt
```

#### 4. 权限问题（Windows PowerShell）
如果遇到执行策略错误：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 离线模式
如果无法连接API服务器，应用程序会：
- 显示连接错误对话框
- 提供重试或离线模式选项
- 使用默认的股票数据继续运行

## 📁 文件结构

```
app/windows/
├── run.py                 # 新的启动脚本（推荐）
├── run_main.py           # 原有启动脚本
├── requirements.txt      # Python依赖
├── src/                  # 源代码目录
│   ├── main_window.py   # 主窗口
│   ├── controllers/     # 控制器
│   ├── views/          # 视图组件
│   ├── models/         # 数据模型
│   └── api/            # API客户端
└── test_*.py            # 测试脚本
```

## 🎨 新功能亮点

### 改进的加载状态显示
- ✅ 详细的初始化阶段显示
- ✅ 动态点动画效果
- ✅ 智能状态消息切换
- ✅ 实时数据加载进度
- ✅ API连接状态指示器

### 用户体验提升
- 📍 信息透明度：清楚知道系统正在做什么
- 📊 进度感知：通过具体数量感知进度
- 👁️ 视觉反馈：动态效果表明系统正在工作
- 🔄 状态一致性：所有UI组件状态同步

## 📞 支持

如果遇到问题：
1. 查看控制台输出的错误信息
2. 检查 `test_*.py` 脚本的测试结果
3. 参考 `LOADING_STATES_IMPROVEMENT.md` 了解最新改进

## 🔄 更新日志

### v1.1.0 (最新)
- ✅ 新增详细的加载状态显示
- ✅ 添加动态动画效果
- ✅ 改进用户体验
- ✅ 创建标准启动脚本 `run.py`

### v1.0.0
- 基础Windows应用程序框架
- 股票数据管理
- API集成
