# Windows WinForm风格样式指南

## 概述

本项目采用Windows WinForm风格的设计语言，提供现代化的用户界面体验，支持浅色/深色主题切换。

## 设计理念

### 1. Windows设计语言
- 遵循Microsoft Fluent Design System设计原则
- 使用Windows 11/10的原生控件样式
- 采用Segoe UI字体，确保与系统一致性

### 2. 颜色方案

#### 浅色主题
- **主色调**: `#0078d4` (Windows蓝色)
- **背景色**: `#f3f2f1` (主背景), `#ffffff` (卡片背景)
- **边框色**: `#d2d0ce` (标准边框), `#0078d4` (焦点边框)
- **文本色**: `#323130` (主要文本), `#605e5c` (次要文本)

#### 深色主题
- **主色调**: `#0078d4` (Windows蓝色)
- **背景色**: `#1f1f1f` (主背景), `#2d2d30` (卡片背景)
- **边框色**: `#3c3c3c` (标准边框), `#0078d4` (焦点边框)
- **文本色**: `#ffffff` (主要文本), `#cccccc` (次要文本)

### 3. 交互状态
- **悬停**: 轻微的背景色变化
- **按下**: 更深的背景色
- **禁用**: 降低透明度和对比度
- **焦点**: 蓝色边框高亮

## 组件样式

### 1. 标签 (Label)
```css
QLabel {
    color: #323130; /* 浅色主题 */
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
}
```

### 2. 输入框 (Input)
```css
QLineEdit {
    background-color: #ffffff;
    border: 1px solid #d2d0ce;
    border-radius: 2px;
    padding: 6px 8px;
    color: #323130;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
}
QLineEdit:focus {
    border-color: #0078d4;
}
```

### 3. 下拉框 (ComboBox)
```css
QComboBox {
    background-color: #ffffff;
    border: 1px solid #d2d0ce;
    border-radius: 2px;
    padding: 6px 8px;
    color: #323130;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    min-width: 100px;
}
```

### 4. 按钮 (Button)

#### 主要按钮
```css
QPushButton {
    background-color: #0078d4;
    color: #ffffff;
    border: none;
    border-radius: 2px;
    padding: 8px 16px;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}
```

#### 次要按钮
```css
QPushButton {
    background-color: #ffffff;
    color: #323130;
    border: 1px solid #d2d0ce;
    border-radius: 2px;
    padding: 7px 15px;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}
```

#### 成功按钮
```css
QPushButton {
    background-color: #107c10;
    color: #ffffff;
    border: none;
    border-radius: 2px;
    padding: 8px 16px;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}
```

#### 危险按钮
```css
QPushButton {
    background-color: #d13438;
    color: #ffffff;
    border: none;
    border-radius: 2px;
    padding: 8px 16px;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}
```

### 5. 工具栏 (Toolbar)
```css
QToolBar {
    background-color: #faf9f8;
    border-bottom: 1px solid #d2d0ce;
    spacing: 4px;
    padding: 4px;
}
QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 2px;
    padding: 6px;
    margin: 1px;
}
QToolButton:hover {
    background-color: #f3f2f1;
    border-color: #d2d0ce;
}
```

### 6. 表格 (Table)
```css
QTableWidget {
    background-color: #ffffff;
    border: 1px solid #d2d0ce;
    border-radius: 2px;
    gridline-color: #d2d0ce;
    color: #323130;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
}
QTableWidget::item {
    padding: 6px;
    border: none;
}
QTableWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}
```

### 7. 标签页 (Tab)
```css
QTabWidget::pane {
    border: 1px solid #d2d0ce;
    border-radius: 2px;
    background-color: #ffffff;
}
QTabBar::tab {
    background-color: #faf9f8;
    border: 1px solid #d2d0ce;
    border-bottom: none;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    padding: 8px 16px;
    margin-right: 2px;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 12px;
    color: #323130;
}
QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom: 1px solid #ffffff;
}
```

## 主题切换

### 1. 主题管理器
```python
from src.styles import ThemeManager, Theme

# 设置主题
ThemeManager.set_theme(Theme.LIGHT)  # 浅色主题
ThemeManager.set_theme(Theme.DARK)   # 深色主题

# 切换主题
ThemeManager.toggle_theme()

# 获取当前主题
current_theme = ThemeManager.get_theme()
```

### 2. 主题切换器组件
```python
from src.views.theme_switcher import ThemeSwitcher

# 创建主题切换器
theme_switcher = ThemeSwitcher()
theme_switcher.theme_changed.connect(self._on_theme_changed)
```

### 3. 样式刷新
```python
# 刷新组件样式
def refresh_styles(self):
    self.setStyleSheet(get_style("label"))
    self.combo.setStyleSheet(get_style("combobox"))
    self.button.setStyleSheet(get_style("button_primary"))
```

## 使用指南

### 1. 导入样式
```python
from src.styles import get_style, ThemeManager, Theme
```

### 2. 应用样式
```python
# 使用get_style函数
label.setStyleSheet(get_style("label"))
button.setStyleSheet(get_style("button_primary"))
combo.setStyleSheet(get_style("combobox"))
```

### 3. 主题变化处理
```python
def _on_theme_changed(self, theme):
    # 刷新所有组件样式
    self.refresh_styles()
    # 更新状态栏
    self.status_bar.showMessage(f"已切换到{theme}主题")
```

### 4. 组件样式刷新
```python
def refresh_styles(self):
    """刷新组件样式"""
    # 更新标签样式
    for child in self.findChildren(QLabel):
        child.setStyleSheet(get_style("label"))
        
    # 更新按钮样式
    for child in self.findChildren(QPushButton):
        child.setStyleSheet(get_style("button_primary"))
```

## 最佳实践

### 1. 字体使用
- 主要文本：Segoe UI, 12px
- 标题文本：Segoe UI, 16px, font-weight: 600
- 按钮文本：Segoe UI, 12px, font-weight: 500

### 2. 间距规范
- 组件间距：8px
- 内边距：6px-8px
- 外边距：4px-8px

### 3. 圆角规范
- 按钮：2px
- 输入框：2px
- 卡片：4px

### 4. 颜色使用
- 优先使用主题颜色变量
- 避免硬编码颜色值
- 确保对比度符合可访问性标准

## 测试

运行测试脚本验证样式：
```bash
cd app/windows
python test_styles.py
```

测试内容包括：
- 基础组件样式
- 按钮样式
- 表格样式
- 主题切换功能
- 样式刷新功能

## 扩展

### 1. 添加新组件样式
1. 在`src/styles.py`中添加新的样式定义
2. 在`get_style`函数中添加对应的样式字符串
3. 在向后兼容函数中添加映射

### 2. 添加新主题
1. 在`COLORS`字典中添加新主题的颜色方案
2. 更新`Theme`枚举
3. 测试新主题的显示效果

### 3. 自定义样式
```python
# 组合多个样式
combined_style = get_style("label") + "font-weight: bold; font-size: 14px;"
label.setStyleSheet(combined_style)
``` 