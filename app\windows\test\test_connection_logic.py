#!/usr/bin/env python3
"""
API连接检查逻辑测试脚本
测试修改后的启动时API检查逻辑
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from api.stock_data_client import StockDataClient
from models.initializer import StockDataInitializer


def test_connection_logic():
    """测试连接检查逻辑"""
    print("=" * 60)
    print("API连接检查逻辑测试")
    print("=" * 60)
    
    # 测试1: 使用正确的API URL
    print("\n1. 测试正确的API URL...")
    try:
        initializer = StockDataInitializer("http://localhost:8000")
        print("✓ 初始化器创建成功")
        
        # 模拟运行初始化
        print("  正在检查API连接...")
        is_healthy = initializer.api_client.health_check()
        print(f"  API健康检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            print("  ✓ API连接正常，将继续获取数据")
        else:
            print("  ⚠ API连接异常，将使用默认数据")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 测试2: 使用错误的API URL
    print("\n2. 测试错误的API URL...")
    try:
        initializer = StockDataInitializer("http://invalid-url:9999")
        print("✓ 初始化器创建成功")
        
        # 模拟运行初始化
        print("  正在检查API连接...")
        is_healthy = initializer.api_client.health_check()
        print(f"  API健康检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            print("  ⚠ 意外：API连接正常")
        else:
            print("  ✓ API连接异常，将使用默认数据")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 测试3: 测试默认数据
    print("\n3. 测试默认数据...")
    try:
        initializer = StockDataInitializer("http://invalid-url:9999")
        initializer._init_default_data()
        stock_data = initializer.get_stock_data()
        
        print(f"✓ 默认数据初始化成功")
        print(f"  美股数量: {len(stock_data.get('US', {}))}")
        print(f"  A股数量: {len(stock_data.get('CN', {}))}")
        print(f"  加密货币数量: {len(stock_data.get('CRYPTO', {}))}")
        
        # 显示一些示例数据
        if stock_data.get('US'):
            sample_us = list(stock_data['US'].items())[:3]
            print(f"  美股示例: {sample_us}")
            
    except Exception as e:
        print(f"✗ 默认数据测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 连接检查逻辑测试完成")
    print("=" * 60)


def test_client_behavior():
    """测试客户端行为"""
    print("\n" + "=" * 60)
    print("客户端行为测试")
    print("=" * 60)
    
    # 测试1: 正常客户端
    print("\n1. 测试正常客户端...")
    try:
        client = StockDataClient("http://localhost:8000")
        
        # 测试健康检查
        is_healthy = client.health_check()
        print(f"  健康检查: {'正常' if is_healthy else '异常'}")
        
        # 测试获取市场列表
        markets = client.get_available_markets()
        print(f"  市场列表: {len(markets)} 个市场")
        
        # 测试获取股票列表
        stocks = client.get_stock_list("US")
        print(f"  美股列表: {len(stocks)} 只股票")
        
    except Exception as e:
        print(f"✗ 正常客户端测试失败: {str(e)}")
    
    # 测试2: 错误客户端
    print("\n2. 测试错误客户端...")
    try:
        client = StockDataClient("http://invalid-url:9999")
        
        # 测试健康检查
        is_healthy = client.health_check()
        print(f"  健康检查: {'正常' if is_healthy else '异常'}")
        
        # 测试获取市场列表（应该返回默认数据）
        markets = client.get_available_markets()
        print(f"  市场列表: {len(markets)} 个市场（默认数据）")
        
        # 测试获取股票列表（应该返回默认数据）
        stocks = client.get_stock_list("US")
        print(f"  美股列表: {len(stocks)} 只股票（默认数据）")
        
    except Exception as e:
        print(f"✗ 错误客户端测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 客户端行为测试完成")
    print("=" * 60)


def main():
    """主函数"""
    print("AI Hedge Fund - 连接检查逻辑测试")
    
    # 测试连接检查逻辑
    test_connection_logic()
    
    # 测试客户端行为
    test_client_behavior()
    
    print("\n🎉 所有测试完成！")
    print("\n总结:")
    print("1. API连接正常时：继续获取真实数据")
    print("2. API连接失败时：立即停止后续请求，使用默认数据")
    print("3. UI正常显示，但会显示连接状态和错误提示")
    print("4. 系统可以完全离线运行")


if __name__ == "__main__":
    main() 