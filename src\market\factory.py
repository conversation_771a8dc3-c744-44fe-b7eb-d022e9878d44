from typing import Dict, Type
from .base import MarketProvider
from .us_stock import USStockMarketProvider
from .cn_stock import CNStockMarketProvider
from .crypto import CryptoMarketProvider

class MarketFactory:
    """市场工厂类，用于创建和管理不同的市场提供者"""
    
    _providers: Dict[str, Type[MarketProvider]] = {
        "US": USStockMarketProvider,
        "CN": CNStockMarketProvider,
        "CRYPTO": CryptoMarketProvider
    }
    
    _instances: Dict[str, MarketProvider] = {}
    
    @classmethod
    def get_provider(cls, market_code: str) -> MarketProvider:
        """
        获取指定市场的提供者实例
        
        Args:
            market_code: 市场代码，如 "US", "CN", "CRYPTO"
            
        Returns:
            MarketProvider: 市场提供者实例
            
        Raises:
            ValueError: 如果市场代码无效
        """
        if market_code not in cls._providers:
            raise ValueError(f"Unsupported market code: {market_code}")
            
        if market_code not in cls._instances:
            cls._instances[market_code] = cls._providers[market_code]()
            
        return cls._instances[market_code]
    
    @classmethod
    def register_provider(cls, market_code: str, provider_class: Type[MarketProvider]):
        """
        注册新的市场提供者
        
        Args:
            market_code: 市场代码
            provider_class: 市场提供者类
        """
        cls._providers[market_code] = provider_class
        if market_code in cls._instances:
            del cls._instances[market_code]
    
    @classmethod
    def get_available_markets(cls) -> Dict[str, str]:
        """
        获取所有可用的市场
        
        Returns:
            Dict[str, str]: 市场代码到市场名称的映射
        """
        return {
            code: provider().get_market_info().name
            for code, provider in cls._providers.items()
        }
    
    @classmethod
    def detect_market(cls, symbol: str) -> str:
        """
        根据交易代码自动检测市场类型
        
        Args:
            symbol: 交易代码
            
        Returns:
            str: 市场代码
            
        Raises:
            ValueError: 如果无法识别市场类型
        """
        # 尝试每个市场提供者
        for market_code, provider_class in cls._providers.items():
            provider = provider_class()
            if provider.validate_symbol(symbol):
                return market_code
                
        raise ValueError(f"Unable to detect market for symbol: {symbol}") 