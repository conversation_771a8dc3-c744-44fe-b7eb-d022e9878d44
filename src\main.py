import sys

from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph
from colorama import Fore, Style, init
import questionary
from src.agents.portfolio_manager import portfolio_management_agent
from src.agents.risk_manager import risk_management_agent
from src.graph.state import AgentState
from src.utils.display import print_trading_output
from src.utils.analysts import ANALYST_ORDER, get_analyst_nodes
from src.utils.progress import progress
from src.llm.models import LLM_ORDER, OLLAMA_LLM_ORDER, get_model_info, ModelProvider
from src.utils.ollama import ensure_ollama_and_model
from src.market.factory import MarketFactory

import argparse
from datetime import datetime
from dateutil.relativedelta import relativedelta
from src.utils.visualize import save_graph_as_png
import json

# Load environment variables from .env file
load_dotenv()

init(autoreset=True)


def parse_hedge_fund_response(response):
    """Parses a JSON string and returns a dictionary."""
    try:
        return json.loads(response)
    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}\nResponse: {repr(response)}")
        return None
    except TypeError as e:
        print(f"Invalid response type (expected string, got {type(response).__name__}): {e}")
        return None
    except Exception as e:
        print(f"Unexpected error while parsing response: {e}\nResponse: {repr(response)}")
        return None


##### Run the Hedge Fund #####
def run_hedge_fund(
    tickers: list[str],
    start_date: str,
    end_date: str,
    portfolio: dict,
    market_provider: str,
    model_name: str,
    model_provider: str,
    selected_analysts: list[str] = None,
    use_all_analysts: bool = False,
):
    # Start progress tracking
    progress.start()

    try:
        # Create a new workflow if analysts are customized
        if selected_analysts:
            workflow = create_workflow(selected_analysts)
            agent = workflow.compile()
        else:
            agent = app

        final_state = agent.invoke(
            {
                "messages": [
                    HumanMessage(
                        content="Make trading decisions based on the provided data.",
                    )
                ],
                "data": {
                    "tickers": tickers,
                    "portfolio": portfolio,
                    "start_date": start_date,
                    "end_date": end_date,
                    "analyst_signals": {},
                },
                "metadata": {
                    "model_name": model_name,
                    "model_provider": model_provider,
                },
            },
        )

        return {
            "decisions": parse_hedge_fund_response(final_state["messages"][-1].content),
            "analyst_signals": final_state["data"]["analyst_signals"],
        }
    finally:
        # Stop progress tracking
        progress.stop()


def start(state: AgentState):
    """Initialize the workflow with the input message."""
    return state


def create_workflow(selected_analysts=None):
    """Create the workflow with selected analysts."""
    workflow = StateGraph(AgentState)
    workflow.add_node("start_node", start)

    # Get analyst nodes from the configuration
    analyst_nodes = get_analyst_nodes()

    # Default to all analysts if none selected
    if selected_analysts is None:
        selected_analysts = list(analyst_nodes.keys())
    # Add selected analyst nodes
    for analyst_key in selected_analysts:
        node_name, node_func = analyst_nodes[analyst_key]
        workflow.add_node(node_name, node_func)
        workflow.add_edge("start_node", node_name)

    # Always add risk and portfolio management
    workflow.add_node("risk_management_agent", risk_management_agent)
    workflow.add_node("portfolio_manager", portfolio_management_agent)

    # Connect selected analysts to risk management
    for analyst_key in selected_analysts:
        node_name = analyst_nodes[analyst_key][0]
        workflow.add_edge(node_name, "risk_management_agent")

    workflow.add_edge("risk_management_agent", "portfolio_manager")
    workflow.add_edge("portfolio_manager", END)

    workflow.set_entry_point("start_node")
    return workflow


def parse_args():
    parser = argparse.ArgumentParser(description="AI量化交易系统")
    parser.add_argument(
        "--tickers",
        type=str,
        required=True,
        help="逗号分隔的股票代码列表 (例如: AAPL,MSFT,GOOGL 或 600000,000001,BTC/USDT)",
    )
    parser.add_argument(
        "--market",
        type=str,
        choices=MarketFactory.get_available_markets().keys(),
        help="市场类型 (US: 美股, CN: A股, CRYPTO: 加密货币)",
    )
    parser.add_argument(
        "--end-date",
        type=str,
        default=datetime.now().strftime("%Y-%m-%d"),
        help="结束日期 (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--start-date",
        type=str,
        help="开始日期 (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--initial-cash",
        type=float,
        default=100000,
        help="初始资金",
    )
    parser.add_argument(
        "--margin-requirement",
        type=float,
        default=0.0,
        help="保证金要求比例 (例如: 0.5 表示 50%)",
    )
    parser.add_argument(
        "--analysts",
        type=str,
        help="逗号分隔的分析师列表",
    )
    parser.add_argument(
        "--analysts-all",
        action="store_true",
        help="使用所有可用的分析师",
    )
    parser.add_argument(
        "--ollama",
        action="store_true",
        help="使用Ollama进行本地LLM推理",
    )
    
    return parser.parse_args()


def main():
    args = parse_args()
    
    # 解析股票代码
    tickers = [ticker.strip() for ticker in args.tickers.split(",")]
    
    # 如果没有指定市场，尝试自动检测
    if not args.market:
        try:
            market = MarketFactory.detect_market(tickers[0])
        except ValueError as e:
            print(f"Error: {e}")
            print("Please specify the market type using --market")
            return
    else:
        market = args.market
    
    # 获取市场提供者
    try:
        provider = MarketFactory.get_provider(market)
    except ValueError as e:
        print(f"Error: {e}")
        return
    
    # 验证所有股票代码
    invalid_tickers = [ticker for ticker in tickers if not provider.validate_symbol(ticker)]
    if invalid_tickers:
        print(f"Error: Invalid tickers for {market} market: {', '.join(invalid_tickers)}")
        return
    
    # 验证日期
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y-%m-%d")
        except ValueError:
            print("Error: Start date must be in YYYY-MM-DD format")
            return
    
    try:
        datetime.strptime(args.end_date, "%Y-%m-%d")
    except ValueError:
        print("Error: End date must be in YYYY-MM-DD format")
        return
    
    # 设置开始和结束日期
    end_date = args.end_date
    if not args.start_date:
        # 计算结束日期前3个月
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
        start_date = (end_date_obj - relativedelta(months=3)).strftime("%Y-%m-%d")
    else:
        start_date = args.start_date
    
    # 初始化投资组合
    portfolio = {
        "cash": args.initial_cash,
        "margin_requirement": args.margin_requirement,
        "margin_used": 0.0,
        "positions": {
            ticker: {
                "long": 0,
                "short": 0,
                "long_cost_basis": 0.0,
                "short_cost_basis": 0.0,
                "short_margin_used": 0.0,
            }
            for ticker in tickers
        },
        "realized_gains": {
            ticker: {
                "long": 0.0,
                "short": 0.0,
            }
            for ticker in tickers
        },
    }
    
    # 运行分析
    run_hedge_fund(
        tickers=tickers,
        start_date=start_date,
        end_date=end_date,
        portfolio=portfolio,
        market_provider=provider,
        model_name="gpt-4o" if not args.ollama else "llama2",
        model_provider="OpenAI" if not args.ollama else "Ollama",
        selected_analysts=args.analysts.split(",") if args.analysts else None,
        use_all_analysts=args.analysts_all,
    )


if __name__ == "__main__":
    main()
