#!/usr/bin/env python3
"""
检查实际的路径情况
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from utils.config_manager import get_config_manager
    from api.client import APILogger
    
    print("=== 实际路径检查 ===")
    
    # ConfigManager路径
    config_manager = get_config_manager()
    config_file_path = config_manager.get_config_file_path()
    log_dir_path = config_manager.get_log_directory()
    
    print(f"ConfigManager配置文件: {config_file_path}")
    print(f"ConfigManager日志目录: {log_dir_path}")
    
    # APILogger路径
    api_logger = APILogger()
    api_log_dir = api_logger.log_dir
    
    print(f"APILogger日志目录: {api_log_dir}")
    
    # 检查是否真的在同一个目录
    config_base = os.path.dirname(config_file_path)
    log_base = os.path.dirname(log_dir_path)
    
    print(f"\n配置文件基础目录: {config_base}")
    print(f"日志目录基础目录: {log_base}")
    print(f"是否在同一目录: {'是' if config_base == log_base else '否'}")
    
    # 检查实际存在的文件
    print(f"\n文件存在检查:")
    print(f"配置文件存在: {'是' if os.path.exists(config_file_path) else '否'}")
    print(f"日志目录存在: {'是' if os.path.exists(log_dir_path) else '否'}")
    
    if os.path.exists(log_dir_path):
        log_files = os.listdir(log_dir_path)
        print(f"日志文件: {log_files}")

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
