from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from datetime import datetime
from pydantic import BaseModel

class MarketData(BaseModel):
    """市场数据基类"""
    open: float
    close: float
    high: float
    low: float
    volume: float
    time: str
    currency: str  # 货币单位
    exchange: str  # 交易所

class MarketInfo(BaseModel):
    """市场信息基类"""
    name: str  # 市场名称
    code: str  # 市场代码
    currency: str  # 货币单位
    timezone: str  # 时区
    trading_hours: Dict[str, List[str]]  # 交易时间
    trading_days: List[str]  # 交易日

class MarketProvider(ABC):
    """市场数据提供者抽象基类"""
    
    @abstractmethod
    def get_market_info(self) -> MarketInfo:
        """获取市场基本信息"""
        pass
    
    @abstractmethod
    def get_price_data(self, symbol: str, start_date: str, end_date: str) -> List[MarketData]:
        """获取价格数据"""
        pass
    
    @abstractmethod
    def get_financial_metrics(self, symbol: str, end_date: str) -> Dict:
        """获取财务指标"""
        pass
    
    @abstractmethod
    def get_company_info(self, symbol: str) -> Dict:
        """获取公司信息"""
        pass
    
    @abstractmethod
    def get_news(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        """获取新闻数据"""
        pass
    
    @abstractmethod
    def get_insider_trades(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        """获取内部交易数据"""
        pass
    
    @abstractmethod
    def validate_symbol(self, symbol: str) -> bool:
        """验证交易代码是否有效"""
        pass
    
    @abstractmethod
    def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        pass 