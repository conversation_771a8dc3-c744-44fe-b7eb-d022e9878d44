#!/usr/bin/env python3
"""
AI量化交易系统 - Windows应用程序启动器
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    from main_window import MainWindow
except ImportError as e:
    print(f"错误：无法导入PyQt6模块: {e}")
    print("请确保已安装PyQt6:")
    print("pip install PyQt6")
    sys.exit(1)


def main():
    """主函数"""
    print("=" * 60)
    print("AI量化交易系统 - Windows应用程序")
    print("=" * 60)
    print("正在启动应用程序...")
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("AI量化交易系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Hedge Fund")
    
    # 启用高DPI支持
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    try:
        # 创建主窗口
        print("正在创建主窗口...")
        window = MainWindow()
        
        # 显示窗口
        print("正在显示窗口...")
        window.show()
        
        print("✓ 应用程序启动成功")
        print("=" * 60)
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        print("请检查以下问题:")
        print("1. 确保所有依赖已正确安装")
        print("2. 确保API服务器正在运行（如果需要）")
        print("3. 检查网络连接")
        sys.exit(1)


if __name__ == "__main__":
    main()
