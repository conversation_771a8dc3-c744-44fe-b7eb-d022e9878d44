from PyQt6.QtWidgets import QMenuBar, QMenu
from PyQt6.QtGui import QAction
from PyQt6.QtCore import pyqtSignal


class MenuBar(QMenuBar):
    """菜单栏组件"""
    
    # 信号定义
    new_triggered = pyqtSignal()
    open_triggered = pyqtSignal()
    save_triggered = pyqtSignal()
    settings_triggered = pyqtSignal()
    about_triggered = pyqtSignal()
    toolbar_toggled = pyqtSignal(bool)
    statusbar_toggled = pyqtSignal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_menus()
        
    def _init_menus(self):
        """初始化菜单"""
        self._create_file_menu()
        self._create_edit_menu()
        self._create_view_menu()
        self._create_tools_menu()
        self._create_help_menu()
        
    def _create_file_menu(self):
        """创建文件菜单"""
        file_menu = self.addMenu("文件")
        
        # 新建
        new_action = QAction("新建", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_triggered.emit)
        file_menu.addAction(new_action)
        
        # 打开
        open_action = QAction("打开", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_triggered.emit)
        file_menu.addAction(open_action)
        
        # 保存
        save_action = QAction("保存", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_triggered.emit)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.parent().close)
        file_menu.addAction(exit_action)
        
    def _create_edit_menu(self):
        """创建编辑菜单"""
        edit_menu = self.addMenu("编辑")
        
        # 撤销
        undo_action = QAction("撤销", self)
        undo_action.setShortcut("Ctrl+Z")
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做", self)
        redo_action.setShortcut("Ctrl+Y")
        edit_menu.addAction(redo_action)
        
    def _create_view_menu(self):
        """创建视图菜单"""
        view_menu = self.addMenu("视图")
        
        # 工具栏
        toolbar_action = QAction("工具栏", self)
        toolbar_action.setCheckable(True)
        toolbar_action.setChecked(True)
        toolbar_action.triggered.connect(self.toolbar_toggled.emit)
        view_menu.addAction(toolbar_action)
        
        # 状态栏
        statusbar_action = QAction("状态栏", self)
        statusbar_action.setCheckable(True)
        statusbar_action.setChecked(True)
        statusbar_action.triggered.connect(self.statusbar_toggled.emit)
        view_menu.addAction(statusbar_action)
        
    def _create_tools_menu(self):
        """创建工具菜单"""
        tools_menu = self.addMenu("工具")
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.settings_triggered.emit)
        tools_menu.addAction(settings_action)
        
    def _create_help_menu(self):
        """创建帮助菜单"""
        help_menu = self.addMenu("帮助")
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.about_triggered.emit)
        help_menu.addAction(about_action) 