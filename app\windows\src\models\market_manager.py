from typing import Dict, Optional, List, Any
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from api.stock_data_client import StockDataClient

class MarketProvider:
    """市场提供者基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码是否有效"""
        return True
    
    def get_market_info(self):
        """获取市场信息"""
        return {"name": self.name}

class USStockMarketProvider(MarketProvider):
    """美股市场提供者"""
    
    def __init__(self):
        super().__init__("美股")
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证美股代码是否有效"""
        # 简单的美股代码验证：3-5个字母
        return len(symbol) >= 3 and len(symbol) <= 5 and symbol.isalpha()

class CNStockMarketProvider(MarketProvider):
    """A股市场提供者"""
    
    def __init__(self):
        super().__init__("A股")
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证A股代码是否有效"""
        # 简单的A股代码验证：6位数字
        return len(symbol) == 6 and symbol.isdigit()

class CryptoMarketProvider(MarketProvider):
    """加密货币市场提供者"""
    
    def __init__(self):
        super().__init__("加密货币")
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证加密货币代码是否有效"""
        # 简单的加密货币代码验证：包含/
        return "/" in symbol

class MarketManager:
    """市场管理器，负责管理不同市场的提供者"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.current_market: Optional[MarketProvider] = None
        self.market_providers: Dict[str, MarketProvider] = {
            "US": USStockMarketProvider(),
            "CN": CNStockMarketProvider(),
            "CRYPTO": CryptoMarketProvider()
        }
        
        # 初始化API客户端
        self.api_client = StockDataClient(api_base_url)
        
        # 默认使用美股市场
        self.switch_market("US")
    
    def switch_market(self, market_code: str) -> bool:
        """切换市场
        
        Args:
            market_code: 市场代码，如 "US", "CN", "CRYPTO"
            
        Returns:
            bool: 切换是否成功
        """
        if market_code in self.market_providers:
            self.current_market = self.market_providers[market_code]
            return True
        return False
    
    def get_current_provider(self) -> Optional[MarketProvider]:
        """获取当前市场的提供者"""
        return self.current_market
    
    def get_available_markets(self) -> Dict[str, str]:
        """获取可用的市场列表
        
        Returns:
            Dict[str, str]: 市场代码到市场名称的映射
        """
        try:
            return self.api_client.get_available_markets()
        except Exception as e:
            print(f"获取市场列表失败，使用默认列表: {str(e)}")
            return {
                "US": "美股",
                "CN": "A股",
                "CRYPTO": "加密货币"
            }
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码在当前市场是否有效
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 是否有效
        """
        if not self.current_market:
            return False
        
        # 首先使用本地验证
        local_valid = self.current_market.validate_symbol(symbol)
        if not local_valid:
            return False
        
        # 然后使用API验证
        try:
            # 获取当前市场代码
            current_market_code = None
            for code, provider in self.market_providers.items():
                if provider == self.current_market:
                    current_market_code = code
                    break
            
            if current_market_code:
                return self.api_client.validate_symbol(current_market_code, symbol)
        except Exception as e:
            print(f"API验证失败，使用本地验证结果: {str(e)}")
        
        return local_valid
    
    def get_market_info(self):
        """获取当前市场的信息"""
        if not self.current_market:
            return None
        return self.current_market.get_market_info()
    
    def get_stock_list(self, market: str) -> Dict[str, str]:
        """获取指定市场的股票列表
        
        Args:
            market: 市场代码
            
        Returns:
            Dict[str, str]: 股票代码到股票名称的映射
        """
        try:
            return self.api_client.get_stock_list(market)
        except Exception as e:
            print(f"获取股票列表失败: {str(e)}")
            return self._get_default_stocks(market)
    
    def _get_default_stocks(self, market: str) -> Dict[str, str]:
        """获取默认股票列表"""
        default_stocks = {
            "US": {
                "AAPL": "Apple Inc.",
                "MSFT": "Microsoft Corporation",
                "GOOGL": "Alphabet Inc.",
                "AMZN": "Amazon.com Inc.",
                "NVDA": "NVIDIA Corporation",
                "META": "Meta Platforms Inc.",
                "TSLA": "Tesla Inc.",
                "JPM": "JPMorgan Chase & Co.",
                "V": "Visa Inc.",
                "UNH": "UnitedHealth Group Inc."
            },
            "CN": {
                "600519": "贵州茅台",
                "000858": "五粮液",
                "601318": "中国平安",
                "600036": "招商银行",
                "000333": "美的集团",
                "600276": "恒瑞医药",
                "601166": "兴业银行",
                "600887": "伊利股份",
                "000651": "格力电器",
                "601888": "中国中免"
            },
            "CRYPTO": {
                "BTC/USDT": "Bitcoin",
                "ETH/USDT": "Ethereum",
                "BNB/USDT": "Binance Coin",
                "SOL/USDT": "Solana",
                "XRP/USDT": "Ripple",
                "ADA/USDT": "Cardano",
                "DOGE/USDT": "Dogecoin",
                "DOT/USDT": "Polkadot",
                "AVAX/USDT": "Avalanche",
                "MATIC/USDT": "Polygon"
            }
        }
        return default_stocks.get(market, {})
    
    def get_price_data(self, market: str, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取价格数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 价格数据列表
        """
        try:
            return self.api_client.get_price_data(market, symbol, start_date, end_date)
        except Exception as e:
            print(f"获取价格数据失败: {str(e)}")
            return []
    
    def get_financial_metrics(self, market: str, symbol: str, end_date: str) -> Dict[str, Any]:
        """获取财务指标
        
        Args:
            market: 市场代码
            symbol: 股票代码
            end_date: 结束日期
            
        Returns:
            Dict[str, Any]: 财务指标数据
        """
        try:
            return self.api_client.get_financial_metrics(market, symbol, end_date)
        except Exception as e:
            print(f"获取财务指标失败: {str(e)}")
            return {}
    
    def get_company_info(self, market: str, symbol: str) -> Dict[str, Any]:
        """获取公司信息
        
        Args:
            market: 市场代码
            symbol: 股票代码
            
        Returns:
            Dict[str, Any]: 公司信息
        """
        try:
            return self.api_client.get_company_info(market, symbol)
        except Exception as e:
            print(f"获取公司信息失败: {str(e)}")
            return {}
    
    def get_news(self, market: str, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取新闻数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 新闻数据列表
        """
        try:
            return self.api_client.get_news(market, symbol, start_date, end_date)
        except Exception as e:
            print(f"获取新闻数据失败: {str(e)}")
            return []
    
    def get_insider_trades(self, market: str, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取内部交易数据
        
        Args:
            market: 市场代码
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 内部交易数据列表
        """
        try:
            return self.api_client.get_insider_trades(market, symbol, start_date, end_date)
        except Exception as e:
            print(f"获取内部交易数据失败: {str(e)}")
            return []
    
    def check_api_health(self) -> bool:
        """检查API服务器健康状态
        
        Returns:
            bool: API服务器是否健康
        """
        try:
            return self.api_client.health_check()
        except Exception as e:
            print(f"API健康检查失败: {str(e)}")
            return False
    
    def close(self):
        """关闭API客户端连接"""
        try:
            self.api_client.close()
        except Exception as e:
            print(f"关闭API客户端失败: {str(e)}") 