#!/usr/bin/env python3
"""
测试流式请求日志功能
专门测试CaseInsensitiveDict等不可JSON序列化对象的处理
"""

import sys
import os
import json
from requests.structures import CaseInsensitiveDict

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from api.client import APIClient, APILogger
    from api.hedge_fund_client import HedgeFundClient
    from api.logger_config import enable_full_api_logging

    def test_case_insensitive_dict_serialization():
        """测试CaseInsensitiveDict序列化"""
        print("=== 测试CaseInsensitiveDict序列化 ===")
        
        logger = APILogger()
        
        # 创建CaseInsensitiveDict对象
        headers = CaseInsensitiveDict({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer token123',
            'User-Agent': 'TestClient/1.0'
        })
        
        print("1. 原始CaseInsensitiveDict:")
        print(f"   类型: {type(headers)}")
        print(f"   内容: {dict(headers)}")
        
        print("\n2. 测试安全序列化:")
        try:
            serialized = logger._safe_serialize(headers)
            print("   ✅ 序列化成功:")
            print(f"   {serialized}")
        except Exception as e:
            print(f"   ❌ 序列化失败: {e}")
        
        print("\n3. 测试make_serializable:")
        try:
            serializable = logger._make_serializable(headers)
            print("   ✅ 转换成功:")
            print(f"   类型: {type(serializable)}")
            print(f"   内容: {serializable}")
        except Exception as e:
            print(f"   ❌ 转换失败: {e}")

    def test_complex_objects_serialization():
        """测试复杂对象序列化"""
        print("\n=== 测试复杂对象序列化 ===")
        
        logger = APILogger()
        
        # 创建包含各种类型的复杂对象
        complex_data = {
            'string': 'test',
            'number': 123,
            'boolean': True,
            'null': None,
            'list': [1, 2, 3],
            'dict': {'nested': 'value'},
            'case_insensitive_dict': CaseInsensitiveDict({'key': 'value'}),
            'tuple': (1, 2, 3),
            'set': {1, 2, 3}  # set不能直接JSON序列化
        }
        
        print("1. 原始复杂数据:")
        for key, value in complex_data.items():
            print(f"   {key}: {type(value)} = {value}")
        
        print("\n2. 测试安全序列化:")
        try:
            serialized = logger._safe_serialize(complex_data)
            print("   ✅ 序列化成功:")
            print(f"   {serialized}")
        except Exception as e:
            print(f"   ❌ 序列化失败: {e}")

    def test_streaming_data_logging():
        """测试流式数据日志记录"""
        print("\n=== 测试流式数据日志记录 ===")
        
        hf_client = HedgeFundClient("http://localhost:8000", debug=True)
        
        # 模拟各种流式数据
        test_data = [
            {
                'type': 'simple_dict',
                'data': {'message': 'test', 'progress': 50}
            },
            {
                'type': 'complex_dict',
                'data': {
                    'headers': CaseInsensitiveDict({'Content-Type': 'application/json'}),
                    'nested': {'deep': {'value': 123}},
                    'list': [1, 2, 3, 4, 5]
                }
            },
            {
                'type': 'string_data',
                'data': 'Simple string message'
            },
            {
                'type': 'large_data',
                'data': {'large_list': list(range(100)), 'description': 'Large dataset'}
            }
        ]
        
        for i, test_case in enumerate(test_data, 1):
            print(f"\n{i}. 测试 {test_case['type']}:")
            try:
                log_result = hf_client._log_streaming_data(test_case['data'], test_case['type'])
                print(f"   ✅ 日志记录成功:")
                print(f"   {log_result}")
            except Exception as e:
                print(f"   ❌ 日志记录失败: {e}")

    def test_api_client_with_problematic_data():
        """测试API客户端处理问题数据"""
        print("\n=== 测试API客户端处理问题数据 ===")
        
        client = APIClient("http://localhost:8000", debug=True)
        
        # 模拟包含CaseInsensitiveDict的请求
        problematic_headers = CaseInsensitiveDict({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer secret-token',
            'Custom-Header': 'custom-value'
        })
        
        problematic_data = {
            'normal_field': 'value',
            'headers': problematic_headers,
            'nested': {
                'case_dict': CaseInsensitiveDict({'nested': 'value'})
            }
        }
        
        print("1. 测试请求日志记录:")
        try:
            client.logger.log_request(
                'POST', 
                'http://localhost:8000/test',
                params={'param': 'value'},
                data=problematic_data,
                headers=problematic_headers
            )
            print("   ✅ 请求日志记录成功")
        except Exception as e:
            print(f"   ❌ 请求日志记录失败: {e}")
        
        print("\n2. 测试响应日志记录:")
        try:
            response_data = {
                'result': 'success',
                'headers': problematic_headers,
                'metadata': CaseInsensitiveDict({'response': 'data'})
            }
            client.logger.log_response(
                'POST',
                'http://localhost:8000/test',
                200,
                response_data,
                1.5
            )
            print("   ✅ 响应日志记录成功")
        except Exception as e:
            print(f"   ❌ 响应日志记录失败: {e}")
        
        client.close()

    def test_edge_cases():
        """测试边界情况"""
        print("\n=== 测试边界情况 ===")
        
        logger = APILogger()
        
        edge_cases = [
            ('None值', None),
            ('空字典', {}),
            ('空列表', []),
            ('空字符串', ''),
            ('循环引用', None),  # 我们会特殊处理
            ('自定义对象', type('CustomObj', (), {'attr': 'value'})()),
            ('函数对象', lambda x: x),
            ('超长字符串', 'x' * 2000)
        ]
        
        # 创建循环引用
        circular = {'self': None}
        circular['self'] = circular
        edge_cases[4] = ('循环引用', circular)
        
        for name, data in edge_cases:
            print(f"\n测试 {name}:")
            try:
                result = logger._safe_serialize(data)
                print(f"   ✅ 处理成功: {len(result)} 字符")
                if len(result) < 200:  # 只显示短结果
                    print(f"   结果: {result}")
                else:
                    print(f"   结果: {result[:100]}...[截断]")
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")

    def main():
        """主测试函数"""
        print("流式请求日志功能测试")
        print("=" * 50)
        
        # 启用完整日志
        enable_full_api_logging()
        
        try:
            # 测试CaseInsensitiveDict序列化
            test_case_insensitive_dict_serialization()
            
            # 测试复杂对象序列化
            test_complex_objects_serialization()
            
            # 测试流式数据日志
            test_streaming_data_logging()
            
            # 测试API客户端
            test_api_client_with_problematic_data()
            
            # 测试边界情况
            test_edge_cases()
            
            print("\n" + "=" * 50)
            print("✅ 所有流式日志测试完成！")
            print("\n主要改进:")
            print("1. ✅ 安全处理CaseInsensitiveDict对象")
            print("2. ✅ 智能转换不可序列化对象")
            print("3. ✅ 流式数据专门处理方法")
            print("4. ✅ 边界情况容错处理")
            print("5. ✅ 保持日志可读性")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
