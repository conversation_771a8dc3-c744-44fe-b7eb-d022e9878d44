#!/usr/bin/env python3
"""
调试AI顾问选择问题
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
    from PyQt6.QtCore import QTimer
    from views.common.agent_selector import AgentSelector
    
    class DebugWindow(QMainWindow):
        """调试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("AI顾问选择调试")
            self.setGeometry(100, 100, 800, 600)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # 添加说明
            info_label = QLabel("调试AI顾问选择问题")
            info_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
            layout.addWidget(info_label)
            
            # 创建AI顾问选择器（单选模式）
            self.agent_selector = AgentSelector(multi_select=False)
            self.agent_selector.agents_changed.connect(self.on_agents_changed)
            layout.addWidget(self.agent_selector)
            
            # 添加测试按钮
            self.test_btn = QPushButton("测试获取选中的AI顾问")
            self.test_btn.clicked.connect(self.test_get_selected)
            layout.addWidget(self.test_btn)
            
            # 状态显示
            self.status_label = QLabel("状态: 等待初始化...")
            layout.addWidget(self.status_label)
            
            # 延迟检查初始状态
            QTimer.singleShot(1000, self.check_initial_state)
        
        def on_agents_changed(self, agents):
            """处理AI顾问选择变化"""
            print(f"=== 主窗口接收到agents_changed信号 ===")
            print(f"  agents: {agents}")
            self.status_label.setText(f"选中的AI顾问: {agents}")
        
        def test_get_selected(self):
            """测试获取选中的AI顾问"""
            selected = self.agent_selector.get_selected_agents()
            print(f"=== 手动获取选中的AI顾问 ===")
            print(f"  结果: {selected}")
            self.status_label.setText(f"手动获取结果: {selected}")
        
        def check_initial_state(self):
            """检查初始状态"""
            print(f"=== 检查初始状态 ===")
            selected = self.agent_selector.get_selected_agents()
            print(f"  初始选中的AI顾问: {selected}")
            self.status_label.setText(f"初始状态: {selected}")

    def main():
        """主函数"""
        app = QApplication(sys.argv)
        
        # 创建调试窗口
        window = DebugWindow()
        window.show()
        
        print("AI顾问选择调试已启动")
        print("观察控制台输出，检查:")
        print("1. AgentSelector初始化时是否触发信号")
        print("2. get_selected_agents()是否返回正确结果")
        print("3. 信号连接是否正常工作")
        
        sys.exit(app.exec())

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt6和相关依赖")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
