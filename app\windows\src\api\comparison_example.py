"""
继承APIClient vs 直接使用的对比示例
展示两种方式的优缺点
"""

import requests
from typing import Dict, Any, Optional
from urllib.parse import urljoin
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


# 方式1: 继承APIClient (当前采用的方式)
class HedgeFundClientInherited(APIClient):
    """继承方式 - 简洁、可扩展"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        super().__init__(base_url)  # 自动获得所有基础功能
        self.base_endpoint = "/api/hedge-fund"
    
    def get_agents(self) -> Dict[str, Any]:
        """自动使用父类的get方法、错误处理、重试机制"""
        try:
            return self.get(f"{self.base_endpoint}/agents")
        except Exception as e:
            print(f"获取代理列表失败: {str(e)}")
            return {"agents": []}
    
    def get_language_models(self) -> Dict[str, Any]:
        """自动使用父类的get方法、错误处理、重试机制"""
        try:
            return self.get(f"{self.base_endpoint}/language-models")
        except Exception as e:
            print(f"获取语言模型列表失败: {str(e)}")
            return {"models": []}
    
    # 可以轻松扩展功能
    def get_agents_with_cache(self) -> Dict[str, Any]:
        """扩展功能示例 - 添加缓存逻辑"""
        if hasattr(self, '_agents_cache'):
            return self._agents_cache
        
        result = self.get_agents()
        self._agents_cache = result
        return result


# 方式2: 直接使用 (不推荐的方式)
class HedgeFundClientDirect:
    """直接使用方式 - 重复代码、难以维护"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # 需要重复实现重试机制
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 需要重复设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        self.base_endpoint = "/api/hedge-fund"
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """需要重复实现请求逻辑"""
        url = urljoin(self.base_url, endpoint)
        
        try:
            response = self.session.request(method=method, url=url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {method} {url}")
            print(f"错误详情: {str(e)}")
            raise
    
    def get_agents(self) -> Dict[str, Any]:
        """需要独立实现错误处理"""
        try:
            return self._make_request('GET', f"{self.base_endpoint}/agents")
        except Exception as e:
            print(f"获取代理列表失败: {str(e)}")
            return {"agents": []}
    
    def get_language_models(self) -> Dict[str, Any]:
        """需要独立实现错误处理"""
        try:
            return self._make_request('GET', f"{self.base_endpoint}/language-models")
        except Exception as e:
            print(f"获取语言模型列表失败: {str(e)}")
            return {"models": []}
    
    def health_check(self) -> bool:
        """需要重复实现健康检查"""
        try:
            self._make_request('GET', '/ping', timeout=3)
            return True
        except Exception as e:
            print(f"API健康检查失败: {str(e)}")
            return False


# 方式3: 组合方式 (另一种选择)
class HedgeFundClientComposition:
    """组合方式 - 使用APIClient作为组件"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.api_client = APIClient(base_url)  # 使用组合而不是继承
        self.base_endpoint = "/api/hedge-fund"
    
    def get_agents(self) -> Dict[str, Any]:
        """通过组合使用APIClient的功能"""
        try:
            return self.api_client.get(f"{self.base_endpoint}/agents")
        except Exception as e:
            print(f"获取代理列表失败: {str(e)}")
            return {"agents": []}
    
    def get_language_models(self) -> Dict[str, Any]:
        """通过组合使用APIClient的功能"""
        try:
            return self.api_client.get(f"{self.base_endpoint}/language-models")
        except Exception as e:
            print(f"获取语言模型列表失败: {str(e)}")
            return {"models": []}
    
    def health_check(self) -> bool:
        """通过组合使用APIClient的功能"""
        return self.api_client.health_check()


def demonstrate_differences():
    """演示不同方式的差异"""
    print("=== 继承方式 vs 直接使用 vs 组合方式对比 ===\n")
    
    # 1. 代码行数对比
    print("1. 代码行数对比:")
    print("   继承方式: ~50行 (简洁)")
    print("   直接使用: ~100行 (重复代码多)")
    print("   组合方式: ~40行 (简洁)")
    print()
    
    # 2. 功能对比
    print("2. 功能对比:")
    print("   继承方式: ✅ 自动获得所有基础功能")
    print("   直接使用: ❌ 需要重复实现基础功能")
    print("   组合方式: ✅ 通过组合获得基础功能")
    print()
    
    # 3. 扩展性对比
    print("3. 扩展性对比:")
    print("   继承方式: ✅ 可以覆盖和扩展方法")
    print("   直接使用: ❌ 无法扩展基础功能")
    print("   组合方式: ⚠️  扩展性有限")
    print()
    
    # 4. 维护性对比
    print("4. 维护性对比:")
    print("   继承方式: ✅ 基础功能更新自动受益")
    print("   直接使用: ❌ 需要手动更新每个客户端")
    print("   组合方式: ✅ 基础功能更新自动受益")
    print()
    
    # 5. 推荐程度
    print("5. 推荐程度:")
    print("   继承方式: 🥇 推荐 (当前采用)")
    print("   直接使用: ❌ 不推荐")
    print("   组合方式: 🥈 备选方案")


if __name__ == "__main__":
    demonstrate_differences() 