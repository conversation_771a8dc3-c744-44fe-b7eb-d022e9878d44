from PyQt6.QtWidgets import Q<PERSON>oolBar, QToolButton, QFrame, QWidget, QSizePolicy
from PyQt6.QtCore import pyqtSignal, QSize
from PyQt6.QtGui import QIcon
from styles import get_style


class RibbonToolBar(QToolBar):
    """Ribbon工具栏组件"""
    
    # 信号定义
    new_clicked = pyqtSignal()
    open_clicked = pyqtSignal()
    save_clicked = pyqtSignal()
    settings_clicked = pyqtSignal()
    help_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMovable(False)
        self.setIconSize(QSize(32, 32))
        self.setStyleSheet(get_style("toolbar"))
        self._init_toolbar()
        
    def _init_toolbar(self):
        """初始化工具栏"""
        self._create_file_buttons()
        self._create_separator()
        self._create_utility_buttons()
        self._create_spacer()
        
    def _create_file_buttons(self):
        """创建文件操作按钮"""
        # 新建按钮
        new_btn = QToolButton()
        new_btn.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_FileIcon))
        new_btn.setToolTip("新建分析任务")
        new_btn.clicked.connect(self.new_clicked.emit)
        self.addWidget(new_btn)
        
        # 打开按钮
        open_btn = QToolButton()
        open_btn.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_DialogOpenButton))
        open_btn.setToolTip("打开分析任务")
        open_btn.clicked.connect(self.open_clicked.emit)
        self.addWidget(open_btn)
        
        # 保存按钮
        save_btn = QToolButton()
        save_btn.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_DialogSaveButton))
        save_btn.setToolTip("保存分析任务")
        save_btn.clicked.connect(self.save_clicked.emit)
        self.addWidget(save_btn)
        
    def _create_separator(self):
        """创建分隔符"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setStyleSheet(get_style("separator"))
        self.addWidget(separator)
        
    def _create_utility_buttons(self):
        """创建工具按钮"""
        # 设置按钮
        settings_btn = QToolButton()
        settings_btn.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_FileDialogDetailedView))
        settings_btn.setToolTip("系统设置")
        settings_btn.clicked.connect(self.settings_clicked.emit)
        self.addWidget(settings_btn)
        
        # 帮助按钮
        help_btn = QToolButton()
        help_btn.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_MessageBoxQuestion))
        help_btn.setToolTip("帮助")
        help_btn.clicked.connect(self.help_clicked.emit)
        self.addWidget(help_btn)
        
    def _create_spacer(self):
        """创建弹性空间"""
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.addWidget(spacer)
        
    def refresh_styles(self):
        """刷新样式"""
        self.setStyleSheet(get_style("toolbar")) 