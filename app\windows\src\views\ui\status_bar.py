from PyQt6.QtWidgets import QStatusBar, QLabel
from PyQt6.QtCore import QTimer
from styles import get_style


class StatusBar(QStatusBar):
    """状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(get_style("statusbar"))
        self._init_status_bar()
        
    def _init_status_bar(self):
        """初始化状态栏"""
        # 左侧：状态信息
        self.status_label = QLabel("就绪")
        self.addWidget(self.status_label)
        
        # 右侧：面包屑导航
        self.breadcrumb_label = QLabel("首页")
        self.breadcrumb_label.setStyleSheet(get_style("breadcrumb_label"))
        self.addPermanentWidget(self.breadcrumb_label)
        
    def update_status(self, message: str, timeout: int = 3000):
        """更新状态信息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
            
    def update_breadcrumb(self, breadcrumb: str):
        """更新面包屑导航"""
        self.breadcrumb_label.setText(breadcrumb)
        
    def refresh_styles(self):
        """刷新样式"""
        self.setStyleSheet(get_style("statusbar"))
        self.breadcrumb_label.setStyleSheet(get_style("breadcrumb_label")) 