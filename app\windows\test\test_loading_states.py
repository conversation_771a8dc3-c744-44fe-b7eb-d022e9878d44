#!/usr/bin/env python3
"""
测试加载状态显示的脚本
验证改进后的加载遮罩状态显示
"""

import sys
import os
import time

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import QTimer, QThread, pyqtSignal
from views.ui.loading_overlay import LoadingOverlay


class MockInitializer(QThread):
    """模拟初始化器"""
    progress = pyqtSignal(str)
    finished = pyqtSignal()
    
    def run(self):
        """模拟初始化过程"""
        steps = [
            ("开始获取股票数据...", 0.5),
            ("正在加载美股股票列表...", 2),
            ("美股股票列表加载完成 (500 只股票)", 0.5),
            ("正在加载A股股票列表...", 2),
            ("A股股票列表加载完成 (4000 只股票)", 0.5),
            ("正在加载加密货币列表...", 1.5),
            ("加密货币列表加载完成 (100 种货币)", 0.5),
            ("正在合并全部股票数据...", 1),
            ("股票数据初始化完成 (共 4600 个标的)", 0.5),
        ]
        
        for message, delay in steps:
            self.progress.emit(message)
            time.sleep(delay)
            
        self.finished.emit()


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("加载状态测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建加载遮罩
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.show()
        self.loading_overlay.show_message("正在启动系统...")
        self.loading_overlay.update_status("正在检查API连接...")
        
        # 启动模拟初始化
        self.start_mock_initialization()
        
    def start_mock_initialization(self):
        """启动模拟初始化"""
        self.initializer = MockInitializer()
        self.initializer.progress.connect(self.on_progress)
        self.initializer.finished.connect(self.on_finished)
        self.initializer.start()
        
    def on_progress(self, message):
        """处理进度更新"""
        print(f"进度: {message}")
        self.loading_overlay.update_status(message)
        
        # 根据不同的初始化阶段更新加载消息
        if "系统初始化开始" in message:
            self.loading_overlay.show_message("正在初始化系统...")
        elif "正在执行初始化任务" in message:
            self.loading_overlay.show_message("正在加载数据...")
        elif "开始获取股票数据" in message:
            self.loading_overlay.show_message("正在获取股票数据...")
        elif "正在加载美股" in message:
            self.loading_overlay.show_message("正在加载美股数据...")
        elif "正在加载A股" in message:
            self.loading_overlay.show_message("正在加载A股数据...")
        elif "正在加载加密货币" in message:
            self.loading_overlay.show_message("正在加载加密货币数据...")
        elif "正在合并" in message:
            self.loading_overlay.show_message("正在整理数据...")
        elif "初始化完成" in message:
            self.loading_overlay.show_message("初始化完成")
            
    def on_finished(self):
        """处理初始化完成"""
        print("初始化完成")
        self.loading_overlay.show_message("系统初始化完成")
        self.loading_overlay.update_status("正在进入系统...")
        
        # 延迟隐藏遮罩
        QTimer.singleShot(2000, self.loading_overlay.hide)
        
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if hasattr(self, 'loading_overlay'):
            self.loading_overlay.resize(self.size())


def test_loading_states():
    """测试加载状态"""
    print("=" * 60)
    print("加载状态显示测试")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("✓ 测试窗口已创建")
    print("✓ 加载遮罩已显示")
    print("✓ 模拟初始化已开始")
    print("\n观察窗口中的加载状态变化...")
    print("预期看到以下状态变化:")
    print("1. 正在连接服务器... - 正在验证API服务器连接...")
    print("2. 正在加载美股数据... - 正在加载美股股票列表...")
    print("3. 正在加载A股数据... - 正在加载A股股票列表...")
    print("4. 正在加载加密货币数据... - 正在加载加密货币列表...")
    print("5. 正在整理数据... - 正在合并全部股票数据...")
    print("6. 初始化完成 - 股票数据初始化完成")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    test_loading_states()
