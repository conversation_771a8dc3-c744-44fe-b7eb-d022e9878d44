# AI量化交易系统 - 模块化结构文档

## 概述

本项目采用模块化设计，按照功能将组件分为不同的模块，提高代码的可维护性和可扩展性。

## 目录结构

```
app/windows/src/
├── main_window.py          # 主窗口
├── styles.py              # 样式管理
├── __init__.py            # 模块初始化
├── controllers/           # 控制器模块
│   ├── __init__.py
│   └── main_controller.py # 主控制器
├── models/                # 数据模型模块
│   ├── __init__.py
│   ├── market_manager.py
│   ├── market_cache.py
│   └── initializer.py
└── views/                 # 视图模块
    ├── __init__.py        # 视图模块初始化
    ├── ui/                # UI基础组件
    │   ├── __init__.py
    │   ├── menu_bar.py    # 菜单栏
    │   ├── toolbar.py     # 工具栏
    │   ├── navigation_tree.py # 导航树
    │   ├── status_bar.py  # 状态栏
    │   └── loading_overlay.py # 加载遮罩层
    ├── analysis/          # 分析功能组件
    │   ├── __init__.py
    │   ├── stock_input.py # 股票输入
    │   ├── date_range.py  # 日期范围选择
    │   ├── results_view.py # 结果显示
    │   ├── agent_selector.py # AI顾问选择
    │   ├── single_stock_single_agent.py # 单股单顾问页面
    │   └── single_stock_multi_agent.py # 单股多顾问页面
    ├── settings/          # 设置功能组件
    │   ├── __init__.py
    │   ├── theme_switcher.py # 主题切换
    │   └── llm_config.py  # LLM配置
    └── common/            # 通用组件
        ├── __init__.py
        └── page_manager.py # 页面管理器
```

## 模块说明

### 1. UI模块 (`views/ui/`)

包含基础的UI组件，这些组件不包含业务逻辑，只负责界面显示和用户交互。

- **menu_bar.py**: 菜单栏组件，包含文件、编辑、视图、工具、帮助等菜单
- **toolbar.py**: 工具栏组件，提供快速操作按钮
- **navigation_tree.py**: 导航树组件，提供左侧导航功能
- **status_bar.py**: 状态栏组件，显示系统状态和面包屑导航
- **loading_overlay.py**: 加载遮罩层组件，显示系统初始化进度

### 2. 分析模块 (`views/analysis/`)

包含所有与股票分析相关的组件，这些组件包含具体的业务逻辑。

- **stock_input.py**: 股票输入组件，支持多市场股票选择
- **date_range.py**: 日期范围选择组件，支持快速选择和自定义日期
- **results_view.py**: 结果显示组件，展示分析结果
- **agent_selector.py**: AI顾问选择组件，支持单选和多选模式
- **single_stock_single_agent.py**: 单股单顾问分析页面
- **single_stock_multi_agent.py**: 单股多顾问对比页面

### 3. 设置模块 (`views/settings/`)

包含系统设置相关的组件。

- **theme_switcher.py**: 主题切换组件，支持浅色/深色主题
- **llm_config.py**: LLM配置组件，配置AI模型参数

### 4. 通用模块 (`views/common/`)

包含通用的、可复用的组件。

- **page_manager.py**: 页面管理器，负责管理所有分析页面的切换

### 5. 控制器模块 (`controllers/`)

包含业务逻辑控制器，负责处理用户操作和协调各个组件。

- **main_controller.py**: 主控制器，处理主窗口的所有业务逻辑

### 6. 模型模块 (`models/`)

包含数据模型和数据处理逻辑。

- **market_manager.py**: 市场管理器，管理不同市场的数据
- **market_cache.py**: 市场缓存，缓存股票数据
- **initializer.py**: 系统初始化器，负责系统启动时的初始化工作

## 设计原则

### 1. 单一职责原则
每个模块只负责一个特定的功能领域，避免功能混杂。

### 2. 高内聚低耦合
模块内部组件紧密相关，模块间依赖关系清晰。

### 3. 可扩展性
新功能可以通过添加新模块或扩展现有模块来实现。

### 4. 可维护性
代码结构清晰，便于理解和修改。

## 使用方式

### 导入组件
```python
# 导入UI组件
from views.ui.menu_bar import MenuBar
from views.ui.toolbar import RibbonToolBar

# 导入分析组件
from views.analysis.stock_input import StockInputView
from views.analysis.date_range import DateRangeView

# 导入设置组件
from views.settings.theme_switcher import ThemeSwitcher

# 导入通用组件
from views.common.page_manager import PageManager
```

### 添加新功能
1. 确定功能所属模块
2. 在对应模块目录下创建新文件
3. 更新模块的`__init__.py`文件
4. 在主窗口中导入和使用新组件

## 扩展指南

### 添加新的分析功能
1. 在`views/analysis/`目录下创建新的分析组件
2. 在`views/analysis/__init__.py`中导出新组件
3. 在`views/__init__.py`中导入新组件
4. 在页面管理器中添加新页面

### 添加新的设置功能
1. 在`views/settings/`目录下创建新的设置组件
2. 在`views/settings/__init__.py`中导出新组件
3. 在配置管理页面中集成新组件

### 添加新的UI组件
1. 在`views/ui/`目录下创建新的UI组件
2. 在`views/ui/__init__.py`中导出新组件
3. 在主窗口或其他页面中使用新组件

## 注意事项

1. 所有组件都应该实现`refresh_styles()`方法以支持主题切换
2. 组件间的通信应该通过信号槽机制实现
3. 避免在组件中直接访问其他模块的组件，应该通过控制器协调
4. 新添加的组件应该遵循现有的命名规范和代码风格 