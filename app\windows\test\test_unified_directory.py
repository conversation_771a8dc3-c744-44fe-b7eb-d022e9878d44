#!/usr/bin/env python3
"""
测试统一目录：确保日志目录和配置文件目录在同一个位置
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from utils.config_manager import get_config_manager
    from api.client import APILogger
    from api.logger_config import logger_config

    def test_directory_unification():
        """测试目录统一"""
        print("=== 测试目录统一 ===")
        
        # 获取ConfigManager
        config_manager = get_config_manager()
        
        # 获取各个组件的目录
        config_file_path = config_manager.get_config_file_path()
        config_dir = os.path.dirname(config_file_path)
        log_dir_from_config = config_manager.get_log_directory()
        
        print(f"1. ConfigManager配置目录: {config_dir}")
        print(f"2. ConfigManager日志目录: {log_dir_from_config}")
        print(f"3. 配置文件路径: {config_file_path}")
        
        # 检查配置文件和日志目录是否在同一个基础目录下
        # 配置文件在 ~/topai/config.json，日志在 ~/topai/logs/
        # 所以它们的基础目录都应该是 ~/topai
        base_log_dir = os.path.dirname(log_dir_from_config)  # ~/topai/logs -> ~/topai

        print(f"4. 配置目录: {config_dir}")
        print(f"5. 日志基础目录: {base_log_dir}")

        directories_unified = config_dir == base_log_dir
        print(f"6. 目录统一: {'✅' if directories_unified else '❌'}")
        
        return directories_unified, config_dir, log_dir_from_config

    def test_api_logger_directory():
        """测试APILogger目录"""
        print("\n=== 测试APILogger目录 ===")
        
        # 创建APILogger实例
        api_logger = APILogger()
        api_log_dir = api_logger.log_dir
        
        print(f"1. APILogger日志目录: {api_log_dir}")
        
        # 获取ConfigManager的日志目录进行比较
        config_manager = get_config_manager()
        config_log_dir = config_manager.get_log_directory()
        
        print(f"2. ConfigManager日志目录: {config_log_dir}")
        
        # 检查是否相同
        directories_match = api_log_dir == config_log_dir
        print(f"3. 目录匹配: {'✅' if directories_match else '❌'}")
        
        if not directories_match:
            print(f"   差异: APILogger使用 {api_log_dir}")
            print(f"        ConfigManager使用 {config_log_dir}")
        
        return directories_match

    def test_logger_config_directory():
        """测试LoggerConfig目录"""
        print("\n=== 测试LoggerConfig目录 ===")
        
        # 获取LoggerConfig的日志目录
        logger_config_dir = logger_config.get_log_directory()
        
        print(f"1. LoggerConfig日志目录: {logger_config_dir}")
        print(f"2. 使用ConfigManager: {'✅' if logger_config.use_config_manager else '❌'}")
        
        # 与ConfigManager比较
        config_manager = get_config_manager()
        config_log_dir = config_manager.get_log_directory()
        
        directories_match = logger_config_dir == config_log_dir
        print(f"3. 与ConfigManager匹配: {'✅' if directories_match else '❌'}")
        
        return directories_match

    def test_directory_structure():
        """测试目录结构"""
        print("\n=== 测试目录结构 ===")
        
        config_manager = get_config_manager()
        
        # 获取基础目录
        config_file_path = config_manager.get_config_file_path()
        base_dir = os.path.dirname(config_file_path)
        
        print(f"1. 基础目录: {base_dir}")
        print(f"   存在: {'✅' if os.path.exists(base_dir) else '❌'}")
        
        # 检查预期的文件和目录
        expected_items = {
            "config.json": "配置文件",
            "logs": "日志目录"
        }
        
        print("\n2. 目录内容检查:")
        all_exist = True
        for item, description in expected_items.items():
            item_path = os.path.join(base_dir, item)
            exists = os.path.exists(item_path)
            print(f"   {item} ({description}): {'✅' if exists else '❌'}")
            if not exists:
                all_exist = False
        
        # 如果logs目录存在，检查其内容
        logs_dir = os.path.join(base_dir, "logs")
        if os.path.exists(logs_dir):
            try:
                log_files = os.listdir(logs_dir)
                print(f"\n3. 日志文件:")
                if log_files:
                    for log_file in log_files:
                        file_path = os.path.join(logs_dir, log_file)
                        file_size = os.path.getsize(file_path)
                        print(f"   📄 {log_file}: {file_size / 1024:.2f} KB")
                else:
                    print("   空目录")
            except Exception as e:
                print(f"   读取失败: {e}")
        
        return all_exist

    def test_path_consistency():
        """测试路径一致性"""
        print("\n=== 测试路径一致性 ===")
        
        # 收集所有路径
        config_manager = get_config_manager()
        api_logger = APILogger()
        
        paths = {
            "ConfigManager配置文件": config_manager.get_config_file_path(),
            "ConfigManager日志目录": config_manager.get_log_directory(),
            "APILogger日志目录": api_logger.log_dir,
            "LoggerConfig日志目录": logger_config.get_log_directory()
        }
        
        print("1. 所有路径:")
        for name, path in paths.items():
            print(f"   {name}: {path}")
        
        # 检查基础目录是否一致
        base_dirs = set()
        for name, path in paths.items():
            if "日志目录" in name:
                # 日志目录的父目录应该是基础目录
                base_dir = os.path.dirname(path)
            else:
                # 配置文件的目录应该是基础目录
                base_dir = os.path.dirname(path)
            base_dirs.add(base_dir)
        
        print(f"\n2. 基础目录数量: {len(base_dirs)}")
        for base_dir in base_dirs:
            print(f"   {base_dir}")
        
        consistent = len(base_dirs) == 1
        print(f"3. 路径一致性: {'✅' if consistent else '❌'}")
        
        return consistent

    def test_create_test_files():
        """测试创建测试文件"""
        print("\n=== 测试文件创建 ===")
        
        config_manager = get_config_manager()
        
        try:
            # 测试配置文件写入
            test_config = {"test": "unified_directory"}
            config_manager.set_logging_config(test_config)
            print("1. 配置文件写入: ✅")
            
            # 测试日志文件写入
            from api.client import APIClient
            client = APIClient("http://localhost:8000", debug=True)
            client.logger.logger.info("测试统一目录日志写入")
            client.close()
            print("2. 日志文件写入: ✅")
            
            # 验证文件存在
            config_file = config_manager.get_config_file_path()
            log_dir = config_manager.get_log_directory()
            log_file = os.path.join(log_dir, "api_client.log")
            
            config_exists = os.path.exists(config_file)
            log_exists = os.path.exists(log_file)
            
            print(f"3. 配置文件存在: {'✅' if config_exists else '❌'}")
            print(f"4. 日志文件存在: {'✅' if log_exists else '❌'}")
            
            return config_exists and log_exists
            
        except Exception as e:
            print(f"文件创建测试失败: {e}")
            return False

    def main():
        """主测试函数"""
        print("统一目录测试：确保日志和配置在同一位置")
        print("=" * 50)
        
        try:
            # 测试目录统一
            unified, config_dir, log_dir = test_directory_unification()
            
            # 测试APILogger目录
            api_match = test_api_logger_directory()
            
            # 测试LoggerConfig目录
            logger_config_match = test_logger_config_directory()
            
            # 测试目录结构
            structure_ok = test_directory_structure()
            
            # 测试路径一致性
            path_consistent = test_path_consistency()
            
            # 测试文件创建
            file_creation_ok = test_create_test_files()
            
            print("\n" + "=" * 50)
            print("✅ 统一目录测试完成！")
            
            # 总结结果
            all_tests_passed = all([
                unified, api_match, logger_config_match, 
                structure_ok, path_consistent, file_creation_ok
            ])
            
            print(f"\n📊 测试结果总结:")
            print(f"   目录统一: {'✅' if unified else '❌'}")
            print(f"   APILogger匹配: {'✅' if api_match else '❌'}")
            print(f"   LoggerConfig匹配: {'✅' if logger_config_match else '❌'}")
            print(f"   目录结构: {'✅' if structure_ok else '❌'}")
            print(f"   路径一致性: {'✅' if path_consistent else '❌'}")
            print(f"   文件创建: {'✅' if file_creation_ok else '❌'}")
            print(f"   总体结果: {'✅ 全部通过' if all_tests_passed else '❌ 存在问题'}")
            
            if all_tests_passed:
                print("\n🎉 恭喜！日志目录和配置文件目录已成功统一！")
                print(f"   统一位置: {config_dir}")
                print(f"   日志目录: {log_dir}")
            else:
                print("\n⚠️  存在问题，需要进一步检查和修复")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已正确设置Python路径")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
