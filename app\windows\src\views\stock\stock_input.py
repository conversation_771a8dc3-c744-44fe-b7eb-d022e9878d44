import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject
from typing import Dict, List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from models.market_manager import MarketManager
from styles import get_style

class StockData(QObject):
    """股票数据管理类"""
    def __init__(self, market_manager: MarketManager):
        super().__init__()
        self.market_manager = market_manager
        self._data = {}

    def get_stocks(self, market: str) -> Dict[str, str]:
        """获取指定市场的股票数据"""
        if market not in self._data:
            # 从API获取股票数据
            self._data[market] = self.market_manager.get_stock_list(market)
        return self._data.get(market, {})

    def update_stocks(self, market: str, stocks: Dict[str, str]):
        """更新指定市场的股票数据"""
        self._data[market] = stocks

    def set_all_data(self, data: Dict[str, Dict[str, str]]):
        """设置所有股票数据"""
        self._data = data
    
    def refresh_market_data(self, market: str):
        """刷新指定市场的股票数据"""
        if market in self._data:
            del self._data[market]
        self.get_stocks(market)

class StockInputView(QWidget):
    """股票输入视图"""
    stocks_changed = pyqtSignal(list)  # 当股票列表改变时发出信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.market_list = {}  # 市场列表
        self.stock_data = {}   # 股票数据
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 市场选择
        market_layout = QHBoxLayout()
        market_label = QLabel("市场:")
        market_label.setStyleSheet(get_style("label"))
        self.market_combo = QComboBox()
        self.market_combo.setStyleSheet(get_style("combobox"))
        # 不在这里填充市场列表
        self.market_combo.currentIndexChanged.connect(self._on_market_changed)
        market_layout.addWidget(market_label)
        market_layout.addWidget(self.market_combo)
        market_layout.addStretch()

        # 股票选择
        stock_layout = QHBoxLayout()
        stock_label = QLabel("股票:")
        stock_label.setStyleSheet(get_style("label"))
        self.stock_combo = QComboBox()
        self.stock_combo.setStyleSheet(get_style("combobox"))
        self.stock_combo.setEditable(True)
        self.stock_combo.setMinimumWidth(300)
        self.stock_combo.lineEdit().setPlaceholderText("输入股票代码或名称")
        self.stock_combo.lineEdit().textEdited.connect(self._on_text_edited)
        self.stock_combo.currentIndexChanged.connect(self._on_stock_selected)
        stock_layout.addWidget(stock_label)
        stock_layout.addWidget(self.stock_combo)
        stock_layout.addStretch()

        layout.addLayout(market_layout)
        layout.addLayout(stock_layout)

    def set_market_list(self, market_list: dict):
        """由主控制器注入市场列表"""
        self.market_list = market_list or {}
        self.market_combo.blockSignals(True)
        self.market_combo.clear()
        for code, name in self.market_list.items():
            self.market_combo.addItem(name, code)

        # 自动选择第一个市场作为默认选择
        if self.market_combo.count() > 0:
            self.market_combo.setCurrentIndex(0)
        else:
            self.market_combo.setCurrentIndex(-1)

        self.market_combo.blockSignals(False)

        # 如果有默认选择，触发市场变化事件来更新股票列表
        if self.market_combo.count() > 0:
            self._on_market_changed(0)

    def update_stock_data(self, stock_data: dict):
        """由主控制器注入股票数据"""
        self.stock_data = stock_data or {}
        self._update_stock_list()

    def set_stock_list(self, market_code: str, stock_list: dict):
        """设置指定市场的股票列表"""
        if not self.stock_data:
            self.stock_data = {}
        self.stock_data[market_code] = stock_list or {}

        # 如果当前选中的市场就是这个市场，更新股票列表
        current_market = self.market_combo.currentData()
        if current_market == market_code:
            self._update_stock_list()

    def _on_market_changed(self, index: int):
        self._update_stock_list()

    def _update_stock_list(self):
        if not self.isVisible():
            return
        self.stock_combo.blockSignals(True)
        self.stock_combo.clear()
        market = self.market_combo.currentData()
        if market and market in self.stock_data:
            stocks = self.stock_data[market]
            for code, name in stocks.items():
                self.stock_combo.addItem(f"{name} ({code})", code)

            # 自动选择第一个股票
            if self.stock_combo.count() > 0:
                self.stock_combo.setCurrentIndex(0)
                # 触发股票选择事件
                self.stock_combo.blockSignals(False)
                self._on_stock_selected(0)
                return

        self.stock_combo.setCurrentIndex(-1)
        self.stock_combo.lineEdit().clear()
        self.stock_combo.blockSignals(False)

    def _on_text_edited(self, text: str):
        if not self.isVisible():
            return
        self.stock_combo.blockSignals(True)
        self.stock_combo.clear()
        market = self.market_combo.currentData()
        if market and market in self.stock_data:
            stocks = self.stock_data[market]
            if not text:
                for code, name in stocks.items():
                    self.stock_combo.addItem(f"{name} ({code})", code)
            else:
                for code, name in stocks.items():
                    if text.lower() in code.lower() or text.lower() in name.lower():
                        self.stock_combo.addItem(f"{name} ({code})", code)
        self.stock_combo.setCurrentIndex(-1)
        self.stock_combo.lineEdit().setText(text)
        self.stock_combo.blockSignals(False)
        self.stock_combo.showPopup()

    def _on_stock_selected(self, index: int):
        if not self.isVisible():
            return
        if index >= 0:
            stock_code = self.stock_combo.currentData()
            if stock_code:
                self.stocks_changed.emit([stock_code])

    def refresh_styles(self):
        for child in self.findChildren(QLabel):
            child.setStyleSheet(get_style("label"))
        self.market_combo.setStyleSheet(get_style("combobox"))
        self.stock_combo.setStyleSheet(get_style("combobox"))

    def clear(self):
        self.stock_combo.setCurrentIndex(-1)
        self.stock_combo.lineEdit().clear()

    def get_stocks(self) -> List[str]:
        current_index = self.stock_combo.currentIndex()
        if current_index >= 0:
            stock_code = self.stock_combo.currentData()
            return [stock_code] if stock_code else []
        return []

    def get_market(self) -> str:
        return self.market_combo.currentData() or "" 