# 样式重构说明

## 概述

本次重构将项目中重复的样式定义抽象到统一的样式文件中，提高了代码的可维护性和一致性。

## 重构内容

### 1. 创建统一的样式文件

在 `src/styles.py` 中定义了所有常用的样式：

- **标签样式**: `LABEL_STYLE`
- **下拉框样式**: `COMBOBOX_STYLE`
- **日期编辑框样式**: `DATEEDIT_STYLE`
- **输入框样式**: `INPUT_STYLE`
- **按钮样式**: `BUTTON_STYLE`, `SECONDARY_BUTTON_STYLE`, `SUCCESS_BUTTON_STYLE`, `DANGER_BUTTON_STYLE`, `WARNING_BUTTON_STYLE`
- **标题样式**: `TITLE_STYLE`
- **加载相关样式**: `LOADING_OVERLAY_STYLE`, `LOADING_LABEL_STYLE`, `LOADING_STATUS_STYLE`, `PROGRESS_BAR_STYLE`
- **工具栏样式**: `TOOLBAR_STYLE`, `SEPARATOR_STYLE`
- **开始分析按钮样式**: `START_ANALYZE_BUTTON_STYLE`

### 2. 更新的组件

#### 日期范围组件 (`src/views/date_range.py`)
- 移除了内联样式定义
- 使用统一的样式常量
- 导入: `LABEL_STYLE`, `COMBOBOX_STYLE`, `DATEEDIT_STYLE`, `SECONDARY_BUTTON_STYLE`

#### 股票输入组件 (`src/views/stock_input.py`)
- 移除了内联样式定义
- 使用统一的样式常量
- 导入: `LABEL_STYLE`, `COMBOBOX_STYLE`

#### 主窗口 (`src/main_window.py`)
- 更新了加载遮罩层样式
- 更新了工具栏样式
- 更新了开始分析按钮样式
- 导入: `LOADING_OVERLAY_STYLE`, `LOADING_LABEL_STYLE`, `LOADING_STATUS_STYLE`, `PROGRESS_BAR_STYLE`, `TOOLBAR_STYLE`, `SEPARATOR_STYLE`, `START_ANALYZE_BUTTON_STYLE`

#### 结果视图 (`src/views/results_view.py`)
- 更新了代理名称标签样式
- 导入: `LABEL_STYLE`

### 3. 样式特点

#### 颜色方案
- 主色调: `#007bff` (蓝色)
- 成功色: `#28a745` (绿色)
- 危险色: `#dc3545` (红色)
- 警告色: `#ffc107` (黄色)
- 次要色: `#6c757d` (灰色)
- 文本色: `#495057` (深灰色)

#### 设计原则
- 统一的边框半径: 4px
- 统一的内边距: 5px-8px
- 统一的字体大小: 12px-16px
- 统一的悬停和按下效果
- 响应式设计

### 4. 使用方法

#### 导入样式
```python
from .styles import LABEL_STYLE, BUTTON_STYLE, COMBOBOX_STYLE
```

#### 应用样式
```python
label = QLabel("文本")
label.setStyleSheet(LABEL_STYLE)

button = QPushButton("按钮")
button.setStyleSheet(BUTTON_STYLE)
```

#### 组合样式
```python
# 组合多个样式
combined_style = LABEL_STYLE + "font-weight: bold; font-size: 14px;"
label.setStyleSheet(combined_style)
```

### 5. 测试

创建了 `test_styles.py` 测试脚本来验证所有样式是否正常工作：

```bash
cd app/windows
python test_styles.py
```

### 6. 优势

1. **代码复用**: 避免重复定义相同的样式
2. **一致性**: 确保整个应用的视觉风格一致
3. **可维护性**: 修改样式只需要在一个地方进行
4. **可扩展性**: 新增样式只需要在样式文件中添加
5. **性能**: 减少重复的样式字符串

### 7. 注意事项

1. 样式字符串使用三引号格式，便于多行编辑
2. 颜色值使用十六进制格式，便于统一管理
3. 样式名称使用大写字母和下划线，便于识别
4. 导入时只导入需要的样式，避免不必要的导入

### 8. 未来改进

1. 可以考虑使用CSS文件来管理样式
2. 可以添加主题切换功能
3. 可以添加样式预览工具
4. 可以添加样式验证工具 