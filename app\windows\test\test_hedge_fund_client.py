#!/usr/bin/env python3
"""
对冲基金API客户端测试脚本
用于验证HedgeFundClient的功能是否正常
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api import HedgeFundClient


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试对冲基金API客户端基本功能 ===")
    
    # 创建客户端实例
    client = HedgeFundClient("http://localhost:8000")
    
    # 测试API连接
    print("1. 测试API连接...")
    if client.health_check():
        print("✅ API服务器连接成功")
    else:
        print("❌ API服务器连接失败")
        return False
    
    # 测试获取代理列表
    print("\n2. 测试获取代理列表...")
    try:
        agents_response = client.get_agents()
        agents = agents_response.get('agents', [])
        print(f"✅ 获取到 {len(agents)} 个代理")
        if agents:
            print(f"   示例代理: {agents[:3]}")
    except Exception as e:
        print(f"❌ 获取代理列表失败: {str(e)}")
        return False
    
    # 测试获取语言模型列表
    print("\n3. 测试获取语言模型列表...")
    try:
        models_response = client.get_language_models()
        models = models_response.get('models', [])
        print(f"✅ 获取到 {len(models)} 个语言模型")
        if models:
            print(f"   示例模型: {models[:3]}")
    except Exception as e:
        print(f"❌ 获取语言模型列表失败: {str(e)}")
        return False
    
    # 测试参数验证
    print("\n4. 测试参数验证...")
    validation = client.validate_hedge_fund_request(
        tickers=["AAPL", "GOOGL"],
        selected_agents=["warren_buffett", "peter_lynch"]
    )
    if validation["is_valid"]:
        print("✅ 参数验证通过")
    else:
        print("❌ 参数验证失败:")
        for error in validation["errors"]:
            print(f"   - {error}")
        return False
    
    # 测试默认分析时间段
    print("\n5. 测试默认分析时间段...")
    period = client.get_default_analysis_period()
    print(f"✅ 默认分析时间段: {period['start_date']} 到 {period['end_date']}")
    
    return True


def test_analysis_functionality():
    """测试分析功能"""
    print("\n=== 测试对冲基金分析功能 ===")
    
    client = HedgeFundClient("http://localhost:8000")
    
    # 测试同步分析（简化版）
    print("1. 测试同步分析...")
    try:
        result = client.run_hedge_fund_sync(
            tickers=["AAPL"],
            selected_agents=["warren_buffett"],
            model_name="gpt-4o",
            model_provider="openai",
            initial_cash=100000.0
        )
        
        if "error" in result:
            print(f"❌ 同步分析失败: {result['error']}")
            return False
        else:
            print("✅ 同步分析成功")
            print(f"   决策数量: {len(result.get('decisions', {}))}")
            print(f"   分析师信号数量: {len(result.get('analyst_signals', {}))}")
            
    except Exception as e:
        print(f"❌ 同步分析异常: {str(e)}")
        return False
    
    return True


def test_streaming_functionality():
    """测试流式分析功能"""
    print("\n=== 测试流式分析功能 ===")
    
    client = HedgeFundClient("http://localhost:8000")
    
    # 定义回调函数
    def progress_callback(event):
        event_type = event.get("type")
        data = event.get("data", {})
        
        if event_type == "start":
            print("🚀 开始分析...")
        elif event_type == "progress":
            agent = data.get("agent", "未知")
            ticker = data.get("ticker", "未知")
            status = data.get("status", "未知")
            print(f"📊 {agent} 正在分析 {ticker}: {status}")
    
    def complete_callback(result):
        print("✅ 流式分析完成!")
        print(f"   决策数量: {len(result.get('decisions', {}))}")
        print(f"   分析师信号数量: {len(result.get('analyst_signals', {}))}")
    
    def error_callback(error_msg):
        print(f"❌ 流式分析错误: {error_msg}")
    
    # 测试流式分析
    print("1. 测试流式分析...")
    try:
        result = client.run_hedge_fund_streaming(
            tickers=["AAPL"],
            selected_agents=["warren_buffett"],
            model_name="gpt-4o",
            model_provider="openai",
            initial_cash=100000.0,
            progress_callback=progress_callback,
            complete_callback=complete_callback,
            error_callback=error_callback
        )
        
        if "error" in result:
            print(f"❌ 流式分析失败: {result['error']}")
            return False
        else:
            print("✅ 流式分析成功")
            
    except Exception as e:
        print(f"❌ 流式分析异常: {str(e)}")
        return False
    
    return True


def main():
    """主函数"""
    print("对冲基金API客户端测试")
    print("=" * 50)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败")
        return
    
    # 测试分析功能
    if not test_analysis_functionality():
        print("\n❌ 分析功能测试失败")
        return
    
    # 测试流式分析功能
    if not test_streaming_functionality():
        print("\n❌ 流式分析功能测试失败")
        return
    
    print("\n🎉 所有测试通过!")
    print("对冲基金API客户端功能正常")


if __name__ == "__main__":
    main() 