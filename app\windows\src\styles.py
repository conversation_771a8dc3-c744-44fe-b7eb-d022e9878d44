"""
统一的样式定义文件 - Windows WinForm风格
支持浅色/深色主题切换
"""

from enum import Enum
from typing import Dict

class Theme(Enum):
    """主题枚举"""
    LIGHT = "light"
    DARK = "dark"

class ThemeManager:
    """主题管理器"""
    _current_theme = Theme.LIGHT
    
    @classmethod
    def set_theme(cls, theme: Theme):
        """设置主题"""
        cls._current_theme = theme
    
    @classmethod
    def get_theme(cls) -> Theme:
        """获取当前主题"""
        return cls._current_theme
    
    @classmethod
    def toggle_theme(cls):
        """切换主题"""
        cls._current_theme = Theme.DARK if cls._current_theme == Theme.LIGHT else Theme.LIGHT

# Windows WinForm风格的颜色方案
COLORS = {
    Theme.LIGHT: {
        # 主色调 - Windows蓝色
        "primary": "#0078d4",
        "primary_hover": "#106ebe",
        "primary_pressed": "#005a9e",
        
        # 背景色
        "bg_main": "#f3f2f1",
        "bg_card": "#ffffff",
        "bg_input": "#ffffff",
        "bg_toolbar": "#faf9f8",
        
        # 边框色
        "border": "#d2d0ce",
        "border_focus": "#0078d4",
        "border_disabled": "#edebe9",
        
        # 文本色
        "text_primary": "#323130",
        "text_secondary": "#605e5c",
        "text_disabled": "#a19f9d",
        "text_white": "#ffffff",
        
        # 状态色
        "success": "#107c10",
        "success_hover": "#0e6b0e",
        "warning": "#d83b01",
        "warning_hover": "#c13501",
        "danger": "#d13438",
        "danger_hover": "#c50e1e",
        
        # 阴影
        "shadow": "rgba(0, 0, 0, 0.1)",
        "shadow_hover": "rgba(0, 0, 0, 0.15)",
    },
    Theme.DARK: {
        # 主色调 - Windows蓝色
        "primary": "#0078d4",
        "primary_hover": "#106ebe",
        "primary_pressed": "#005a9e",
        
        # 背景色
        "bg_main": "#1f1f1f",
        "bg_card": "#2d2d30",
        "bg_input": "#3c3c3c",
        "bg_toolbar": "#2d2d30",
        
        # 边框色
        "border": "#3c3c3c",
        "border_focus": "#0078d4",
        "border_disabled": "#2d2d30",
        
        # 文本色
        "text_primary": "#ffffff",
        "text_secondary": "#cccccc",
        "text_disabled": "#6b6b6b",
        "text_white": "#ffffff",
        
        # 状态色
        "success": "#107c10",
        "success_hover": "#0e6b0e",
        "warning": "#d83b01",
        "warning_hover": "#c13501",
        "danger": "#d13438",
        "danger_hover": "#c50e1e",
        
        # 阴影
        "shadow": "rgba(0, 0, 0, 0.3)",
        "shadow_hover": "rgba(0, 0, 0, 0.4)",
    }
}

def get_color(color_name: str) -> str:
    """获取当前主题下的颜色"""
    theme = ThemeManager.get_theme()
    return COLORS[theme][color_name]

def get_style(style_name: str) -> str:
    """获取样式字符串"""
    styles = {
        # 主窗口样式
        "main_window": f"""
            QMainWindow {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_primary")};
            }}
        """,
        
        # 标签样式
        "label": f"""
            QLabel {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }}
        """,
        
        # Tips样式
        "tips": f"""
            QLabel {{
                color: {get_color("text_secondary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 11px;
                font-style: italic;
                padding: 4px 8px;
                background-color: {get_color("bg_main")};
                border: 1px solid {get_color("border")};
                border-radius: 4px;
                margin: 2px 0px;
            }}
        """,
        
        # 页面说明样式
        "page_description": f"""
            QLabel {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 13px;
                font-weight: 500;
                padding: 12px 16px;
                background-color: {get_color("bg_card")};
                border: 1px solid {get_color("border")};
                border-radius: 6px;
                margin: 4px 0px;
                line-height: 1.4;
            }}
        """,
        
        # 标题样式
        "title": f"""
            QLabel {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 16px;
                font-weight: 600;
            }}
        """,

        # 副标题样式
        "subtitle": f"""
            QLabel {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 14px;
                font-weight: 600;
                margin: 6px 0px 4px 0px;
                padding: 2px 0px;
            }}
        """,

        # 输入框样式
        "input": f"""
            QLineEdit {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                padding: 6px 8px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                selection-background-color: {get_color("primary")};
            }}
            QLineEdit:focus {{
                border-color: {get_color("border_focus")};
                outline: none;
            }}
            QLineEdit:disabled {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_disabled")};
                border-color: {get_color("border_disabled")};
            }}
        """,
        
        # 文本编辑框样式
        "text_edit": f"""
            QTextEdit {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                padding: 8px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                selection-background-color: {get_color("primary")};
            }}
            QTextEdit:focus {{
                border-color: {get_color("border_focus")};
                outline: none;
            }}
            QTextEdit:disabled {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_disabled")};
                border-color: {get_color("border_disabled")};
            }}
        """,
        
        # 下拉框样式
        "combobox": f"""
            QComboBox {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                padding: 6px 8px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                min-width: 100px;
            }}
            QComboBox:focus {{
                border-color: {get_color("border_focus")};
            }}
            QComboBox:disabled {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_disabled")};
                border-color: {get_color("border_disabled")};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid {get_color("text_secondary")};
                margin-right: 4px;
            }}
            QComboBox::down-arrow:disabled {{
                border-top-color: {get_color("text_disabled")};
            }}
            QComboBox QAbstractItemView {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                selection-background-color: {get_color("primary")};
                color: {get_color("text_primary")};
                outline: none;
            }}
        """,
        
        # 日期编辑框样式
        "dateedit": f"""
            QDateEdit {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                padding: 6px 8px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                min-width: 100px;
            }}
            QDateEdit:focus {{
                border-color: {get_color("border_focus")};
            }}
            QDateEdit:disabled {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_disabled")};
                border-color: {get_color("border_disabled")};
            }}
            QDateEdit::drop-down {{
                border: none;
                width: 20px;
            }}
            QDateEdit::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid {get_color("text_secondary")};
                margin-right: 4px;
            }}
        """,
        
        # 主要按钮样式
        "button_primary": f"""
            QPushButton {{
                background-color: {get_color("primary")};
                color: {get_color("text_white")};
                border: none;
                border-radius: 2px;
                padding: 8px 16px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {get_color("primary_hover")};
            }}
            QPushButton:pressed {{
                background-color: {get_color("primary_pressed")};
            }}
            QPushButton:disabled {{
                background-color: {get_color("border_disabled")};
                color: {get_color("text_disabled")};
            }}
        """,
        
        # 次要按钮样式
        "button_secondary": f"""
            QPushButton {{
                background-color: {get_color("bg_input")};
                color: {get_color("text_primary")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                padding: 7px 15px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {get_color("bg_main")};
                border-color: {get_color("border_focus")};
            }}
            QPushButton:pressed {{
                background-color: {get_color("border")};
            }}
            QPushButton:disabled {{
                background-color: {get_color("bg_main")};
                color: {get_color("text_disabled")};
                border-color: {get_color("border_disabled")};
            }}
        """,
        
        # 成功按钮样式
        "button_success": f"""
            QPushButton {{
                background-color: {get_color("success")};
                color: {get_color("text_white")};
                border: none;
                border-radius: 2px;
                padding: 8px 16px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {get_color("success_hover")};
            }}
            QPushButton:pressed {{
                background-color: {get_color("success_hover")};
            }}
            QPushButton:disabled {{
                background-color: {get_color("border_disabled")};
                color: {get_color("text_disabled")};
            }}
        """,
        
        # 危险按钮样式
        "button_danger": f"""
            QPushButton {{
                background-color: {get_color("danger")};
                color: {get_color("text_white")};
                border: none;
                border-radius: 2px;
                padding: 8px 16px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {get_color("danger_hover")};
            }}
            QPushButton:pressed {{
                background-color: {get_color("danger_hover")};
            }}
            QPushButton:disabled {{
                background-color: {get_color("border_disabled")};
                color: {get_color("text_disabled")};
            }}
        """,
        
        # 工具栏样式
        "toolbar": f"""
            QToolBar {{
                background-color: {get_color("bg_toolbar")};
                border: none;
                border-bottom: 1px solid {get_color("border")};
                spacing: 4px;
                padding: 4px;
            }}
            QToolBar QToolButton {{
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 2px;
                padding: 4px;
                margin: 1px;
            }}
            QToolBar QToolButton:hover {{
                background-color: {get_color("shadow")};
                border-color: {get_color("border")};
            }}
            QToolBar QToolButton:pressed {{
                background-color: {get_color("shadow_hover")};
                border-color: {get_color("border_focus")};
            }}
        """,
        
        # 面包屑工具栏样式
        "breadcrumb_toolbar": f"""
            QToolBar {{
                background-color: {get_color("bg_main")};
                border: none;
                border-bottom: 1px solid {get_color("border")};
                spacing: 2px;
                padding: 2px 8px;
            }}
        """,
        
        # 面包屑标签样式
        "breadcrumb_label": f"""
            QLabel {{
                color: {get_color("text_secondary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 11px;
                padding: 2px 8px;
                background-color: {get_color("bg_main")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
            }}
        """,
        
        # 树形导航样式
        "tree": f"""
            QTreeWidget {{
                background-color: {get_color("bg_card")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                outline: none;
            }}
            QTreeWidget::item {{
                padding: 4px 8px;
                border: none;
            }}
            QTreeWidget::item:hover {{
                background-color: {get_color("shadow")};
            }}
            QTreeWidget::item:selected {{
                background-color: {get_color("primary")};
                color: {get_color("text_white")};
            }}
            QTreeWidget::branch {{
                background-color: transparent;
            }}
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {{
                image: none;
                border-image: none;
            }}
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {{
                image: none;
                border-image: none;
            }}
        """,
        
        # 分隔符样式
        "separator": f"""
            QFrame {{
                background-color: {get_color("border")};
            }}
        """,
        
        # 卡片样式
        "card": f"""
            QFrame {{
                background-color: {get_color("bg_card")};
                border: 1px solid {get_color("border")};
                border-radius: 4px;
                padding: 16px;
            }}
        """,
        
        # 表格样式
        "table": f"""
            QTableWidget {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                gridline-color: {get_color("border")};
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }}
            QTableWidget::item {{
                padding: 6px;
                border: none;
            }}
            QTableWidget::item:selected {{
                background-color: {get_color("primary")};
                color: {get_color("text_white")};
            }}
            QHeaderView::section {{
                background-color: {get_color("bg_toolbar")};
                border: 1px solid {get_color("border")};
                padding: 6px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
            }}
        """,
        
        # 标签页样式
        "tab": f"""
            QTabWidget::pane {{
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                background-color: {get_color("bg_card")};
            }}
            QTabBar::tab {{
                background-color: {get_color("bg_toolbar")};
                border: 1px solid {get_color("border")};
                border-bottom: none;
                border-top-left-radius: 2px;
                border-top-right-radius: 2px;
                padding: 8px 16px;
                margin-right: 2px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                color: {get_color("text_primary")};
            }}
            QTabBar::tab:selected {{
                background-color: {get_color("bg_card")};
                border-bottom: 1px solid {get_color("bg_card")};
            }}
            QTabBar::tab:hover {{
                background-color: {get_color("bg_main")};
            }}
        """,
        
        # 滚动条样式
        "scrollbar": f"""
            QScrollBar:vertical {{
                background-color: {get_color("bg_main")};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {get_color("border")};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {get_color("text_secondary")};
            }}
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
        """,
        
        # 加载遮罩层样式
        "loading_overlay": f"""
            QFrame {{
                background-color: rgba(0, 0, 0, 0.7);
                border: none;
            }}
        """,
        
        # 加载标签样式
        "loading_label": f"""
            QLabel {{
                color: {get_color("text_white")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 16px;
                font-weight: 600;
            }}
        """,
        
        # 加载状态标签样式
        "loading_status": f"""
            QLabel {{
                color: {get_color("text_secondary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
            }}
        """,
        
        # 进度条样式
        "progress_bar": f"""
            QProgressBar {{
                border: 2px solid {get_color("text_white")};
                border-radius: 4px;
                text-align: center;
                color: {get_color("text_white")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                font-weight: 500;
                background-color: transparent;
            }}
            QProgressBar::chunk {{
                background-color: {get_color("primary")};
                border-radius: 2px;
            }}
        """,
        
        # 单选按钮样式
        "radio_button": f"""
            QRadioButton {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                spacing: 8px;
                padding: 4px;
            }}
            QRadioButton::indicator {{
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid {get_color("border")};
                background-color: {get_color("bg_input")};
            }}
            QRadioButton::indicator:hover {{
                border-color: {get_color("border_focus")};
            }}
            QRadioButton::indicator:checked {{
                border-color: {get_color("primary")};
                background-color: {get_color("bg_input")};
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgOCA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8Y2lyY2xlIGN4PSI0IiBjeT0iNCIgcj0iNCIgZmlsbD0iIzAwNzhkNCIvPgo8L3N2Zz4K);
            }}
            QRadioButton:disabled {{
                color: {get_color("text_disabled")};
            }}
            QRadioButton::indicator:disabled {{
                border-color: {get_color("border_disabled")};
                background-color: {get_color("bg_main")};
            }}
        """,

        # 滚动区域样式
        "scroll_area": f"""
            QScrollArea {{
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                background-color: {get_color("bg_input")};
            }}
            QScrollArea > QWidget > QWidget {{
                background-color: {get_color("bg_input")};
            }}
        """,

        # 列表样式
        "list": f"""
            QListWidget {{
                background-color: {get_color("bg_input")};
                border: 1px solid {get_color("border")};
                border-radius: 2px;
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 12px;
                outline: none;
            }}
            QListWidget::item {{
                padding: 6px 8px;
                border: none;
                border-bottom: 1px solid {get_color("border")};
            }}
            QListWidget::item:hover {{
                background-color: {get_color("shadow")};
            }}
            QListWidget::item:selected {{
                background-color: {get_color("primary")};
                color: {get_color("text_white")};
            }}
        """,

        # 开始分析按钮样式
        "start_analyze_button": f"""
            QPushButton {{
                background-color: {get_color("success")};
                color: {get_color("text_white")};
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 14px;
                font-weight: 600;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {get_color("success_hover")};
            }}
            QPushButton:pressed {{
                background-color: {get_color("success_hover")};
            }}
            QPushButton:disabled {{
                background-color: {get_color("border_disabled")};
                color: {get_color("text_disabled")};
            }}
        """,
        
        # 状态栏样式
        "statusbar": f"""
            QStatusBar {{
                background-color: {get_color("bg_toolbar")};
                border-top: 1px solid {get_color("border")};
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 11px;
            }}
            QStatusBar QLabel {{
                color: {get_color("text_primary")};
                font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
                font-size: 11px;
                padding: 2px 4px;
            }}
        """,
    }
    
    return styles.get(style_name, "")

# 为了向后兼容，保留原有的样式常量
def get_legacy_style(style_name: str) -> str:
    """获取向后兼容的样式"""
    legacy_mapping = {
        "LABEL_STYLE": get_style("label"),
        "TITLE_STYLE": get_style("title"),
        "INPUT_STYLE": get_style("input"),
        "COMBOBOX_STYLE": get_style("combobox"),
        "DATEEDIT_STYLE": get_style("dateedit"),
        "BUTTON_STYLE": get_style("button_primary"),
        "SECONDARY_BUTTON_STYLE": get_style("button_secondary"),
        "SUCCESS_BUTTON_STYLE": get_style("button_success"),
        "DANGER_BUTTON_STYLE": get_style("button_danger"),
        "WARNING_BUTTON_STYLE": get_style("button_danger"),  # 使用危险按钮样式
        "MAIN_WINDOW_STYLE": get_style("main_window"),
        "TOOLBAR_STYLE": get_style("toolbar"),
        "SEPARATOR_STYLE": get_style("separator"),
        "CARD_STYLE": get_style("card"),
        "TABLE_STYLE": get_style("table"),
        "TAB_STYLE": get_style("tab"),
        "SCROLLBAR_STYLE": get_style("scrollbar"),
        "LOADING_OVERLAY_STYLE": get_style("loading_overlay"),
        "LOADING_LABEL_STYLE": get_style("loading_label"),
        "LOADING_STATUS_STYLE": get_style("loading_status"),
        "PROGRESS_BAR_STYLE": get_style("progress_bar"),
        "START_ANALYZE_BUTTON_STYLE": get_style("start_analyze_button"),
    }
    
    return legacy_mapping.get(style_name, "")

# 导出向后兼容的样式常量
LABEL_STYLE = get_legacy_style("LABEL_STYLE")
TITLE_STYLE = get_legacy_style("TITLE_STYLE")
INPUT_STYLE = get_legacy_style("INPUT_STYLE")
COMBOBOX_STYLE = get_legacy_style("COMBOBOX_STYLE")
DATEEDIT_STYLE = get_legacy_style("DATEEDIT_STYLE")
BUTTON_STYLE = get_legacy_style("BUTTON_STYLE")
SECONDARY_BUTTON_STYLE = get_legacy_style("SECONDARY_BUTTON_STYLE")
SUCCESS_BUTTON_STYLE = get_legacy_style("SUCCESS_BUTTON_STYLE")
DANGER_BUTTON_STYLE = get_legacy_style("DANGER_BUTTON_STYLE")
WARNING_BUTTON_STYLE = get_legacy_style("WARNING_BUTTON_STYLE")
MAIN_WINDOW_STYLE = get_legacy_style("MAIN_WINDOW_STYLE")
TOOLBAR_STYLE = get_legacy_style("TOOLBAR_STYLE")
SEPARATOR_STYLE = get_legacy_style("SEPARATOR_STYLE")
CARD_STYLE = get_legacy_style("CARD_STYLE")
TABLE_STYLE = get_legacy_style("TABLE_STYLE")
TAB_STYLE = get_legacy_style("TAB_STYLE")
SCROLLBAR_STYLE = get_legacy_style("SCROLLBAR_STYLE")
LOADING_OVERLAY_STYLE = get_legacy_style("LOADING_OVERLAY_STYLE")
LOADING_LABEL_STYLE = get_legacy_style("LOADING_LABEL_STYLE")
LOADING_STATUS_STYLE = get_legacy_style("LOADING_STATUS_STYLE")
PROGRESS_BAR_STYLE = get_legacy_style("PROGRESS_BAR_STYLE")
START_ANALYZE_BUTTON_STYLE = get_legacy_style("START_ANALYZE_BUTTON_STYLE") 