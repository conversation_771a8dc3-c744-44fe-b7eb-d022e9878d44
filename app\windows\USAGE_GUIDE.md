# AI量化交易系统 - 使用说明

## 系统概述

AI量化交易系统是一个基于Python和PyQt6开发的桌面应用程序，支持股票数据分析和AI投资顾问功能。系统采用前后端分离架构，Windows应用通过API调用获取数据。

## 系统要求

### 软件要求
- Python 3.8+
- PyQt6
- requests
- 其他依赖包（见requirements.txt）

### 硬件要求
- 内存：4GB以上
- 存储：1GB可用空间
- 网络：支持HTTP连接

## 安装和启动

### 1. 安装依赖
```bash
cd app/windows
pip install -r requirements.txt
```

### 2. 启动Backend服务（可选）
```bash
cd app/backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 启动Windows应用
```bash
cd app/windows
python run_main.py
```

## 界面说明

### 主界面布局
- **菜单栏**：文件操作、设置、帮助等
- **工具栏**：常用功能快捷按钮
- **导航树**：功能页面导航
- **主内容区**：当前页面的内容
- **状态栏**：系统状态和API连接状态

### 状态指示器
状态栏右侧的状态指示器显示API连接状态：
- 🟢 **绿色**：API连接正常
- 🟠 **橙色**：API未连接，使用默认数据
- 🔴 **红色**：连接错误
- ⚪ **灰色**：未知状态

## 功能使用

### 1. 股票分析

#### 单股单顾问分析
1. 在导航树中选择"单股单顾问分析"
2. 选择市场（美股/A股/加密货币）
3. 输入或选择股票代码
4. 设置分析日期范围
5. 选择AI投资顾问
6. 点击"开始分析"

#### 多股分析
1. 在导航树中选择"多股分析"
2. 选择市场
3. 添加多个股票
4. 设置分析参数
5. 开始分析

### 2. 设置配置

#### LLM配置
1. 在导航树中选择"设置"
2. 配置LLM模型参数
3. 保存配置

#### 主题切换
1. 在设置页面选择主题
2. 支持浅色/深色主题切换

## 离线模式

### 自动离线模式
当API服务器不可用时，系统会自动切换到离线模式：
- 使用内置的默认股票数据
- 界面正常显示
- 基本功能可用
- 状态指示器显示连接状态

### 手动检查连接
1. 查看状态栏的API连接状态
2. 如果显示"未连接"，检查：
   - 网络连接是否正常
   - API服务器是否已启动
   - 防火墙设置是否允许连接

## 故障排除

### 1. 应用无法启动
**问题**：Python环境或依赖包问题
**解决**：
- 检查Python版本（需要3.8+）
- 重新安装依赖包：`pip install -r requirements.txt`
- 检查PyQt6是否正确安装

### 2. API连接失败
**问题**：无法连接到后端服务
**解决**：
- 确认Backend服务已启动
- 检查网络连接
- 检查防火墙设置
- 系统会自动使用默认数据继续运行

### 3. 股票数据获取失败
**问题**：无法获取实时股票数据
**解决**：
- 检查API服务器状态
- 确认股票代码格式正确
- 系统会使用默认数据进行演示

### 4. 界面显示异常
**问题**：界面样式或布局问题
**解决**：
- 尝试切换主题
- 重启应用
- 检查屏幕分辨率设置

## 测试工具

### API连接测试
```bash
cd app/windows
python test_api_connection.py
```

### 离线模式测试
```bash
cd app/windows
python test_offline_mode.py
```

## 配置说明

### 环境变量配置
```bash
# API服务器地址
export AI_HEDGE_FUND_API_URL="http://localhost:8000"

# 超时时间（秒）
export AI_HEDGE_FUND_API_TIMEOUT="30"

# 重试次数
export AI_HEDGE_FUND_API_RETRY_COUNT="3"
```

### 配置文件
创建 `api_config.json`：
```json
{
  "base_url": "http://localhost:8000",
  "timeout": 30,
  "retry_count": 3,
  "enable_cache": true,
  "cache_duration": 3600
}
```

## 开发说明

### 项目结构
```
app/windows/
├── src/
│   ├── api/              # API客户端
│   ├── controllers/      # 控制器
│   ├── models/          # 数据模型
│   ├── views/           # 界面组件
│   └── main_window.py   # 主窗口
├── test_*.py            # 测试脚本
└── *.md                # 文档
```

### 添加新功能
1. 在 `views/` 中创建新的界面组件
2. 在 `controllers/` 中添加对应的控制器方法
3. 在 `main_window.py` 中注册新页面
4. 更新导航树配置

## 技术支持

### 日志查看
- 应用运行时的错误信息会显示在控制台
- API连接状态会显示在状态栏
- 详细日志可以通过调试模式查看

### 问题报告
如遇到问题，请提供：
- 错误信息截图
- 系统环境信息
- 操作步骤描述
- 日志文件（如有）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持单股和多股分析
- 实现API前后端分离架构
- 支持离线模式运行
- 添加状态指示器和错误处理 