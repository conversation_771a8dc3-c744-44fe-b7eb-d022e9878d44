import sys
import os
from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
)
from PyQt6.QtCore import Qt, pyqtSignal

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .stock_input import StockInputView
from .date_range import DateRangeView
from ..common.analysis_mode import AnalysisMode
from ..common.results_view import ResultsView
from models.market_manager import MarketManager

class StockAnalysis(QWidget):
    """股票分析页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.market_manager = MarketManager()
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # 股票输入
        self.stock_input = StockInputView()
        layout.addWidget(self.stock_input)
        
        # 日期范围
        self.date_range = DateRangeView()
        layout.addWidget(self.date_range)
        
        # 分析模式
        self.analysis_mode = AnalysisMode()
        layout.addWidget(self.analysis_mode)
        
        # 结果显示
        self.results_view = ResultsView()
        layout.addWidget(self.results_view)
        
        self.setLayout(layout)
        
    def _connect_signals(self):
        """连接信号"""
        # 分析按钮点击
        self.analysis_mode.analyze_clicked.connect(self._on_analyze)
        
    def _on_analyze(self):
        """分析处理"""
        # 获取股票列表
        stocks = self.stock_input.get_stocks()
        if not stocks:
            return
            
        # 获取市场
        market = self.stock_input.get_market()
        
        # 获取日期范围
        start_date, end_date = self.date_range.get_date_range()
        
        # 获取分析模式
        mode = self.analysis_mode.get_mode()
        
        # 设置分析状态
        self.analysis_mode.set_analyzing(True)
        
        try:
            # 清空之前的结果
            self.results_view.clear_results()
            
            # 获取市场数据
            market_data = self.market_manager.get_market_data(
                market=market,
                symbols=stocks,
                start_date=start_date,
                end_date=end_date
            )
            
            # 运行分析
            results = self.market_manager.analyze_market_data(
                market_data=market_data,
                mode=mode
            )
            
            # 更新结果
            self.results_view.update_results(results)
            
        finally:
            # 恢复分析状态
            self.analysis_mode.set_analyzing(False) 