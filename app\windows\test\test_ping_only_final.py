#!/usr/bin/env python3
"""
最终API连通性检查测试脚本
验证只调用/ping进行API连通性检查，不触发其他API调用
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from api.client import APIClient
from controllers.main_controller import MainController


def test_main_controller_initialization():
    """测试主控制器的初始化流程"""
    print("=" * 60)
    print("主控制器初始化流程测试")
    print("=" * 60)
    
    # 模拟主窗口对象
    class MockMainWindow:
        def __init__(self):
            self.status_bar = MockStatusBar()
            self.loading_overlay = MockLoadingOverlay()
            self.page_manager = MockPageManager()
            self.setEnabled = lambda x: None
            self.close = lambda: None
            
    class MockStatusBar:
        def update_status(self, message):
            print(f"  状态栏: {message}")
            
    class MockLoadingOverlay:
        def show(self):
            print("  显示加载遮罩层")
        def hide(self):
            print("  隐藏加载遮罩层")
        def raise_(self):
            pass
        def update_status(self, message):
            print(f"  加载遮罩层: {message}")
            
    class MockPageManager:
        def count(self):
            return 0
        def widget(self, i):
            return None
    
    # 测试1: 创建主控制器
    print("\n1. 创建主控制器...")
    try:
        main_window = MockMainWindow()
        controller = MainController(main_window, "http://localhost:8000")
        print("✓ 主控制器创建成功")
        print(f"  market_manager: {controller.market_manager} (应该为None)")
        
    except Exception as e:
        print(f"✗ 主控制器创建失败: {str(e)}")
        return
    
    # 测试2: 模拟API连接检查流程
    print("\n2. 模拟API连接检查流程...")
    try:
        print("  开始API连接检查...")
        controller._check_api_connection()
        print("  ✓ API连接检查完成")
        
    except Exception as e:
        print(f"✗ API连接检查失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ 主控制器初始化流程测试完成")
    print("=" * 60)


def test_api_calls():
    """测试API调用"""
    print("\n" + "=" * 60)
    print("API调用测试")
    print("=" * 60)
    
    # 测试1: 基础APIClient健康检查
    print("\n1. 基础APIClient健康检查...")
    try:
        client = APIClient("http://localhost:8000")
        print("✓ 基础APIClient创建成功")
        
        # 进行健康检查
        print("  正在调用健康检查...")
        is_healthy = client.health_check()
        print(f"  健康检查结果: {'正常' if is_healthy else '异常'}")
        
        if is_healthy:
            print("  ✓ API连接正常，可以进行后续操作")
        else:
            print("  ⚠ API连接异常，程序应该退出")
            
    except Exception as e:
        print(f"✗ 基础APIClient健康检查失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✓ API调用测试完成")
    print("=" * 60)


def test_initialization_sequence():
    """测试初始化序列"""
    print("\n" + "=" * 60)
    print("初始化序列测试")
    print("=" * 60)
    
    print("\n正确的初始化序列应该是:")
    print("1. 创建主控制器 (market_manager = None)")
    print("2. 调用 _check_api_connection()")
    print("3. 使用基础APIClient调用 /ping 或 /")
    print("4. 如果连通性正常，调用 _start_system_initialization()")
    print("5. 创建MarketManager")
    print("6. 创建StockDataInitializer")
    print("7. 开始数据初始化")
    
    print("\n关键点:")
    print("- 在API连接检查阶段，只调用 /ping 或 /")
    print("- 不会调用 /api/stock-data/markets")
    print("- MarketManager在API连接检查通过后才创建")
    print("- 确保不会在连通性检查前触发其他API调用")
    
    print("\n" + "=" * 60)
    print("✓ 初始化序列测试完成")
    print("=" * 60)


def main():
    """主函数"""
    print("AI Hedge Fund - 最终API连通性检查测试")
    
    # 测试主控制器初始化流程
    test_main_controller_initialization()
    
    # 测试API调用
    test_api_calls()
    
    # 测试初始化序列
    test_initialization_sequence()
    
    print("\n🎉 所有测试完成！")
    print("\n总结:")
    print("1. 主控制器创建时，market_manager为None")
    print("2. 启动时只调用 /ping 或 / 进行连通性检查")
    print("3. 使用基础APIClient，避免触发其他API调用")
    print("4. 只有连通性检查通过后，才创建MarketManager")
    print("5. 确保不会在连通性检查阶段调用 /api/stock-data/markets")


if __name__ == "__main__":
    main() 