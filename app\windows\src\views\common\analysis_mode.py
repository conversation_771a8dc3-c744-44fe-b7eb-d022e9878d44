from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QComboBox, QPushButton)
from PyQt6.QtCore import pyqtSignal

class AnalysisMode(QWidget):
    """分析模式选择组件"""
    
    # 定义信号
    mode_changed = pyqtSignal(str)  # 模式变化信号
    analyze_clicked = pyqtSignal()  # 分析按钮点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout()
        
        # 模式选择
        mode_label = QLabel("分析模式:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["实时分析", "回测分析"])
        self.mode_combo.currentTextChanged.connect(self._on_mode_changed)
        layout.addWidget(mode_label)
        layout.addWidget(self.mode_combo)
        
        # 分析按钮
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.clicked.connect(self._on_analyze_clicked)
        layout.addWidget(self.analyze_btn)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def _on_mode_changed(self, mode: str):
        """模式变化处理"""
        self.mode_changed.emit(mode)
        
    def _on_analyze_clicked(self):
        """分析按钮点击处理"""
        self.analyze_clicked.emit()
        
    def get_mode(self) -> str:
        """获取当前选择的分析模式"""
        return self.mode_combo.currentText()
        
    def set_analyzing(self, analyzing: bool):
        """设置分析状态"""
        self.analyze_btn.setEnabled(not analyzing)
        self.analyze_btn.setText("分析中..." if analyzing else "开始分析") 