from typing import List, Dict, Optional
from datetime import datetime
import os
import requests
from .base import MarketProvider, MarketData, MarketInfo
from src.tools.api import (
    get_prices,
    get_financial_metrics,
    get_company_news,
    get_insider_trades,
    get_market_cap,
)

class USStockMarketProvider(MarketProvider):
    """美股市场数据提供者"""
    
    def __init__(self):
        self.api_key = os.environ.get("FINANCIAL_DATASETS_API_KEY")
        self.headers = {"X-API-KEY": self.api_key} if self.api_key else {}
        
    def get_market_info(self) -> MarketInfo:
        return MarketInfo(
            name="US Stock Market",
            code="US",
            currency="USD",
            timezone="America/New_York",
            trading_hours={
                "regular": ["09:30", "16:00"],
                "pre_market": ["04:00", "09:30"],
                "after_hours": ["16:00", "20:00"]
            },
            trading_days=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
        )
    
    def get_price_data(self, symbol: str, start_date: str, end_date: str) -> List[MarketData]:
        prices = get_prices(symbol, start_date, end_date)
        return [
            MarketData(
                open=p.open,
                close=p.close,
                high=p.high,
                low=p.low,
                volume=p.volume,
                time=p.time,
                currency="USD",
                exchange="NYSE/NASDAQ"
            )
            for p in prices
        ]
    
    def get_financial_metrics(self, symbol: str, end_date: str) -> Dict:
        metrics = get_financial_metrics(symbol, end_date)
        if not metrics:
            return {}
        return metrics[0].model_dump()
    
    def get_company_info(self, symbol: str) -> Dict:
        url = f"https://api.financialdatasets.ai/company/facts/?ticker={symbol}"
        response = requests.get(url, headers=self.headers)
        if response.status_code != 200:
            return {}
        return response.json()
    
    def get_news(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        news = get_company_news(symbol, end_date, start_date)
        return [n.model_dump() for n in news]
    
    def get_insider_trades(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        trades = get_insider_trades(symbol, end_date, start_date)
        return [t.model_dump() for t in trades]
    
    def validate_symbol(self, symbol: str) -> bool:
        # 简单验证美股代码格式
        if not symbol:
            return False
        # 美股代码通常为1-5个字母
        return bool(symbol.isalpha() and 1 <= len(symbol) <= 5)
    
    def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        # 这里可以调用专门的交易日历API
        # 暂时返回一个简单的实现
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        days = []
        current = start
        while current <= end:
            if current.weekday() < 5:  # 0-4 表示周一到周五
                days.append(current.strftime("%Y-%m-%d"))
            current = datetime.fromtimestamp(current.timestamp() + 86400)
        return days 