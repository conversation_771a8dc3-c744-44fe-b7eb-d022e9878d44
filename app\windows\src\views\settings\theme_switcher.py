import sys
import os
from PyQt6.QtWidgets import <PERSON>Widge<PERSON>, QHBoxLayout, QPushButton, QLabel
from PyQt6.QtCore import pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import ThemeManager, Theme, get_style

class ThemeSwitcher(QWidget):
    """主题切换组件"""
    
    theme_changed = pyqtSignal(object)  # 主题变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(8)
        
        # 主题标签
        self.theme_label = QLabel("主题:")
        self.theme_label.setStyleSheet(get_style("label"))
        layout.addWidget(self.theme_label)
        
        # 浅色主题按钮
        self.light_btn = QPushButton("浅色")
        self.light_btn.setCheckable(True)
        self.light_btn.setStyleSheet(get_style("button_secondary"))
        self.light_btn.clicked.connect(lambda: self._switch_theme(Theme.LIGHT))
        layout.addWidget(self.light_btn)
        
        # 深色主题按钮
        self.dark_btn = QPushButton("深色")
        self.dark_btn.setCheckable(True)
        self.dark_btn.setStyleSheet(get_style("button_secondary"))
        self.dark_btn.clicked.connect(lambda: self._switch_theme(Theme.DARK))
        layout.addWidget(self.dark_btn)
        
        # 自动切换按钮
        self.auto_btn = QPushButton("自动")
        self.auto_btn.setCheckable(True)
        self.auto_btn.setStyleSheet(get_style("button_secondary"))
        self.auto_btn.clicked.connect(self._switch_auto)
        layout.addWidget(self.auto_btn)
        
        layout.addStretch()
        
        # 更新按钮状态
        self._update_button_states()
        
    def _switch_theme(self, theme: Theme):
        """切换主题"""
        # 取消其他按钮的选中状态
        self.light_btn.setChecked(theme == Theme.LIGHT)
        self.dark_btn.setChecked(theme == Theme.DARK)
        self.auto_btn.setChecked(False)
        
        # 设置主题
        ThemeManager.set_theme(theme)
        
        # 发出信号
        self.theme_changed.emit(theme)
        
    def _switch_auto(self):
        """切换到自动模式"""
        # 取消其他按钮的选中状态
        self.light_btn.setChecked(False)
        self.dark_btn.setChecked(False)
        self.auto_btn.setChecked(True)
        
        # 自动模式：根据当前时间或系统主题选择
        # 暂时使用浅色主题作为默认值
        auto_theme = Theme.LIGHT
        
        # 设置主题
        ThemeManager.set_theme(auto_theme)
        
        # 发出信号
        self.theme_changed.emit(auto_theme)
        
    def _update_button_states(self):
        """更新按钮状态"""
        current_theme = ThemeManager.get_theme()
        self.light_btn.setChecked(current_theme == Theme.LIGHT)
        self.dark_btn.setChecked(current_theme == Theme.DARK)
        self.auto_btn.setChecked(False)
        
    def refresh_styles(self):
        """刷新样式"""
        # 更新组件样式
        self.theme_label.setStyleSheet(get_style("label"))
        self.light_btn.setStyleSheet(get_style("button_secondary"))
        self.dark_btn.setStyleSheet(get_style("button_secondary"))
        self.auto_btn.setStyleSheet(get_style("button_secondary")) 