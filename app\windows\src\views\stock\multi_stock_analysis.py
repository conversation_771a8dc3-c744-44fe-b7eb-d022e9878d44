import sys
import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QListWidget, QListWidgetItem, QTextEdit, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from typing import List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .multi_stock_input import MultiStockInputView
from .date_range import DateRangeView
from ..common.results_view import ResultsView
from ..common.agent_selector import AgentSelector
from styles import get_style

class MultiStockAnalysisPage(QWidget):
    """多股分析页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_stocks = []
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 页面说明
        page_desc = QLabel("📊 多股组合分析：选择多只股票进行组合分析，支持相关性分析、风险分散评估和组合优化建议。")
        page_desc.setStyleSheet(get_style("page_description"))
        page_desc.setWordWrap(True)
        layout.addWidget(page_desc)
        
        # 第一行：股票选择、Agent选择和日期选择
        top_layout = QHBoxLayout()
        
        # 股票输入组件（多选）
        stock_group = QWidget()
        stock_layout = QVBoxLayout(stock_group)
        stock_title = QLabel("股票选择")
        stock_title.setStyleSheet(get_style("title"))
        stock_layout.addWidget(stock_title)
        
        stock_tips = QLabel("💡 选择多只股票进行组合分析，建议选择3-10只股票")
        stock_tips.setStyleSheet(get_style("tips"))
        stock_tips.setWordWrap(True)
        stock_layout.addWidget(stock_tips)
        
        self.stock_input = MultiStockInputView()
        stock_layout.addWidget(self.stock_input)
        
        # 已选股票列表
        selected_stocks_label = QLabel("已选股票:")
        selected_stocks_label.setStyleSheet(get_style("label"))
        stock_layout.addWidget(selected_stocks_label)
        
        self.selected_stocks_list = QListWidget()
        self.selected_stocks_list.setMaximumHeight(120)
        self.selected_stocks_list.setStyleSheet(get_style("list_widget"))
        stock_layout.addWidget(self.selected_stocks_list)
        
        # 清空按钮
        clear_btn = QPushButton("清空选择")
        clear_btn.setStyleSheet(get_style("button_secondary"))
        clear_btn.clicked.connect(self._clear_selected_stocks)
        stock_layout.addWidget(clear_btn)
        
        top_layout.addWidget(stock_group)
        
        # Agent选择组件
        agent_group = QWidget()
        agent_layout = QVBoxLayout(agent_group)
        agent_tips = QLabel("💡 选择AI投资顾问进行组合分析，支持多顾问对比")
        agent_tips.setStyleSheet(get_style("tips"))
        agent_tips.setWordWrap(True)
        agent_layout.addWidget(agent_tips)
        self.agent_selector = AgentSelector(multi_select=True)
        agent_layout.addWidget(self.agent_selector)
        top_layout.addWidget(agent_group)
        
        # 日期范围组件
        date_group = QWidget()
        date_layout = QVBoxLayout(date_group)
        date_title = QLabel("时间范围")
        date_title.setStyleSheet(get_style("title"))
        date_layout.addWidget(date_title)
        self.date_range = DateRangeView()
        date_layout.addWidget(self.date_range)
        top_layout.addWidget(date_group)
        
        layout.addLayout(top_layout)
        
        # 第二行：开始分析按钮
        analyze_layout = QHBoxLayout()
        analyze_layout.addStretch()
        self.start_analyze_btn = QPushButton("开始组合分析")
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        self.start_analyze_btn.setEnabled(False)
        analyze_layout.addWidget(self.start_analyze_btn)
        analyze_layout.addStretch()
        layout.addLayout(analyze_layout)
        
        # 第三行：分析结果
        results_group = QWidget()
        results_layout = QVBoxLayout(results_group)
        results_title = QLabel("组合分析结果")
        results_title.setStyleSheet(get_style("title"))
        results_layout.addWidget(results_title)
        
        # 创建标签页显示不同类型的结果
        self.results_view = ResultsView()
        results_layout.addWidget(self.results_view)
        
        layout.addWidget(results_group)
        
    def _connect_signals(self):
        """连接信号"""
        # 股票选择信号
        if hasattr(self.stock_input, 'stocks_changed'):
            self.stock_input.stocks_changed.connect(self._on_stocks_changed)
            
        # 日期范围信号
        if hasattr(self.date_range, 'date_range_changed'):
            self.date_range.date_range_changed.connect(self._on_date_range_changed)
            
        # Agent选择信号
        if hasattr(self.agent_selector, 'agents_changed'):
            self.agent_selector.agents_changed.connect(self._on_agents_changed)
            
        # 开始分析按钮信号
        self.start_analyze_btn.clicked.connect(self._on_start_analyze)
        
    def _on_stocks_changed(self, stocks: List[str]):
        """股票选择变化处理"""
        self.selected_stocks = stocks
        self._update_selected_stocks_list()
        self._update_analyze_button()
        
    def _update_selected_stocks_list(self):
        """更新已选股票列表"""
        self.selected_stocks_list.clear()
        for stock in self.selected_stocks:
            item = QListWidgetItem(stock)
            self.selected_stocks_list.addItem(item)
            
    def _clear_selected_stocks(self):
        """清空已选股票"""
        self.selected_stocks = []
        self.selected_stocks_list.clear()
        self._update_analyze_button()
        
    def _on_date_range_changed(self, start_date: str, end_date: str):
        """日期范围变化处理"""
        self.current_start_date = start_date
        self.current_end_date = end_date
        self._update_analyze_button()
        
    def _on_agents_changed(self, agents: List[str]):
        """Agent选择变化处理"""
        self.current_agents = agents
        self._update_analyze_button()
        
    def _update_analyze_button(self):
        """更新分析按钮状态"""
        can_analyze = (
            len(self.selected_stocks) >= 2 and 
            hasattr(self, 'current_start_date') and 
            hasattr(self, 'current_end_date') and
            hasattr(self, 'current_agents') and 
            len(self.current_agents) > 0
        )
        self.start_analyze_btn.setEnabled(can_analyze)
        
    def _on_start_analyze(self):
        """开始分析处理"""
        if len(self.selected_stocks) < 2:
            return
            
        # 清空之前的结果
        self.results_view.clear_results()
        
        # 显示分析状态
        self.results_view.update_results({
            'status': 'analyzing',
            'message': f'正在分析 {len(self.selected_stocks)} 只股票的组合表现...'
        })
        
        # TODO: 实现多股分析逻辑
        # 这里应该调用后端API进行多股组合分析
        # 包括相关性分析、风险分散评估、组合优化建议等
        
        # 模拟分析结果
        self._simulate_analysis_results()
        
    def _simulate_analysis_results(self):
        """模拟分析结果（临时）"""
        import time
        time.sleep(1)  # 模拟分析时间
        
        results = {
            'status': 'completed',
            'stocks': self.selected_stocks,
            'analysis': {
                'correlation': '股票间相关性分析结果',
                'risk_diversification': '风险分散评估结果',
                'portfolio_optimization': '组合优化建议',
                'performance_metrics': '组合表现指标'
            }
        }
        
        self.results_view.update_results(results)
        
    def refresh_styles(self):
        """刷新样式"""
        # 更新组件样式
        for child in self.findChildren(QLabel):
            if hasattr(child, 'setStyleSheet'):
                child.setStyleSheet(get_style("label"))
                
        # 更新列表样式
        self.selected_stocks_list.setStyleSheet(get_style("list_widget"))
        
        # 更新按钮样式
        self.start_analyze_btn.setStyleSheet(get_style("start_analyze_button")) 