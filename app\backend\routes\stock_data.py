from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import sys
import os

# 添加主项目路径到Python路径
main_project_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "..")
if main_project_path not in sys.path:
    sys.path.insert(0, main_project_path)

from src.market.factory import MarketFactory
from src.market.base import MarketProvider
from app.backend.models.schemas import (
    StockListResponse,
    PriceDataResponse,
    FinancialMetricsResponse,
    CompanyInfoResponse,
    NewsResponse,
    InsiderTradesResponse,
    MarketInfoResponse
)

router = APIRouter()

@router.get("/markets", response_model=Dict[str, str], tags=["stock-data"])
async def get_available_markets():
    """获取可用的市场列表"""
    return {
        "US": "美股",
        "CN": "A股", 
        "CRYPTO": "加密货币"
    }

@router.get("/stocks/{market}", response_model=StockListResponse, tags=["stock-data"])
async def get_stock_list(market: str):
    """获取指定市场的股票列表"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 根据市场类型返回不同的股票列表
        if market == "US":
            stocks = {
                "AAPL": "Apple Inc.",
                "MSFT": "Microsoft Corporation", 
                "GOOGL": "Alphabet Inc.",
                "AMZN": "Amazon.com Inc.",
                "NVDA": "NVIDIA Corporation",
                "META": "Meta Platforms Inc.",
                "TSLA": "Tesla Inc.",
                "JPM": "JPMorgan Chase & Co.",
                "V": "Visa Inc.",
                "UNH": "UnitedHealth Group Inc."
            }
        elif market == "CN":
            stocks = {
                "600519": "贵州茅台",
                "000858": "五粮液",
                "601318": "中国平安",
                "600036": "招商银行",
                "000333": "美的集团",
                "600276": "恒瑞医药",
                "601166": "兴业银行",
                "600887": "伊利股份",
                "000651": "格力电器",
                "601888": "中国中免"
            }
        elif market == "CRYPTO":
            stocks = {
                "BTC/USDT": "Bitcoin",
                "ETH/USDT": "Ethereum",
                "BNB/USDT": "Binance Coin",
                "SOL/USDT": "Solana",
                "XRP/USDT": "Ripple",
                "ADA/USDT": "Cardano",
                "DOGE/USDT": "Dogecoin",
                "DOT/USDT": "Polkadot",
                "AVAX/USDT": "Avalanche",
                "MATIC/USDT": "Polygon"
            }
        else:
            stocks = {}
            
        return StockListResponse(
            market=market,
            stocks=stocks,
            count=len(stocks)
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")

@router.get("/price/{market}/{symbol}", response_model=PriceDataResponse, tags=["stock-data"])
async def get_price_data(
    market: str,
    symbol: str,
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)")
):
    """获取股票价格数据"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 验证股票代码
        if not provider.validate_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"无效的股票代码: {symbol}")
        
        # 获取价格数据
        price_data = provider.get_price_data(symbol, start_date, end_date)
        
        return PriceDataResponse(
            market=market,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            data=price_data,
            count=len(price_data)
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取价格数据失败: {str(e)}")

@router.get("/financial/{market}/{symbol}", response_model=FinancialMetricsResponse, tags=["stock-data"])
async def get_financial_metrics(
    market: str,
    symbol: str,
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)")
):
    """获取财务指标数据"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 验证股票代码
        if not provider.validate_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"无效的股票代码: {symbol}")
        
        # 获取财务指标
        metrics = provider.get_financial_metrics(symbol, end_date)
        
        return FinancialMetricsResponse(
            market=market,
            symbol=symbol,
            end_date=end_date,
            metrics=metrics
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取财务指标失败: {str(e)}")

@router.get("/company/{market}/{symbol}", response_model=CompanyInfoResponse, tags=["stock-data"])
async def get_company_info(market: str, symbol: str):
    """获取公司信息"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 验证股票代码
        if not provider.validate_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"无效的股票代码: {symbol}")
        
        # 获取公司信息
        info = provider.get_company_info(symbol)
        
        return CompanyInfoResponse(
            market=market,
            symbol=symbol,
            info=info
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取公司信息失败: {str(e)}")

@router.get("/news/{market}/{symbol}", response_model=NewsResponse, tags=["stock-data"])
async def get_news(
    market: str,
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)")
):
    """获取新闻数据"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 验证股票代码
        if not provider.validate_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"无效的股票代码: {symbol}")
        
        # 获取新闻数据
        news = provider.get_news(symbol, start_date, end_date)
        
        return NewsResponse(
            market=market,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            news=news,
            count=len(news)
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取新闻数据失败: {str(e)}")

@router.get("/insider-trades/{market}/{symbol}", response_model=InsiderTradesResponse, tags=["stock-data"])
async def get_insider_trades(
    market: str,
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)")
):
    """获取内部交易数据"""
    try:
        provider = MarketFactory.get_provider(market)
        
        # 验证股票代码
        if not provider.validate_symbol(symbol):
            raise HTTPException(status_code=400, detail=f"无效的股票代码: {symbol}")
        
        # 获取内部交易数据
        trades = provider.get_insider_trades(symbol, start_date, end_date)
        
        return InsiderTradesResponse(
            market=market,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            trades=trades,
            count=len(trades)
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取内部交易数据失败: {str(e)}")

@router.get("/market-info/{market}", response_model=MarketInfoResponse, tags=["stock-data"])
async def get_market_info(market: str):
    """获取市场信息"""
    try:
        provider = MarketFactory.get_provider(market)
        info = provider.get_market_info()
        
        return MarketInfoResponse(
            market=market,
            info=info
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场信息失败: {str(e)}")

@router.get("/validate/{market}/{symbol}", tags=["stock-data"])
async def validate_symbol(market: str, symbol: str):
    """验证股票代码是否有效"""
    try:
        provider = MarketFactory.get_provider(market)
        is_valid = provider.validate_symbol(symbol)
        
        return {
            "market": market,
            "symbol": symbol,
            "is_valid": is_valid
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"验证股票代码失败: {str(e)}") 