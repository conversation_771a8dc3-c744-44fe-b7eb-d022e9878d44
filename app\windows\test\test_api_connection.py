#!/usr/bin/env python3
"""
API连接测试脚本
用于测试Windows应用与Backend API的连接
"""

import sys
import os
import time

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
src_dir = os.path.join(parent_dir, "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from api.stock_data_client import StockDataClient
from config.api_config import api_config


def test_api_connection():
    """测试API连接"""
    print("=" * 50)
    print("API连接测试")
    print("=" * 50)
    
    # 获取API配置
    base_url = api_config.get_base_url()
    print(f"API基础URL: {base_url}")
    print(f"超时时间: {api_config.get_timeout()}秒")
    print(f"重试次数: {api_config.get_retry_count()}")
    print()
    
    # 创建API客户端
    try:
        client = StockDataClient(base_url)
        print("✓ API客户端创建成功")
    except Exception as e:
        print(f"✗ API客户端创建失败: {str(e)}")
        return False
    
    # 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        is_healthy = client.health_check()
        if is_healthy:
            print("✓ API服务器健康检查通过")
        else:
            print("✗ API服务器健康检查失败")
            return False
    except Exception as e:
        print(f"✗ 健康检查异常: {str(e)}")
        return False
    
    # 测试获取市场列表
    print("\n2. 测试获取市场列表...")
    try:
        markets = client.get_available_markets()
        print(f"✓ 获取到 {len(markets)} 个市场:")
        for code, name in markets.items():
            print(f"  - {code}: {name}")
    except Exception as e:
        print(f"✗ 获取市场列表失败: {str(e)}")
        return False
    
    # 测试获取股票列表
    print("\n3. 测试获取股票列表...")
    for market in ["US", "CN", "CRYPTO"]:
        try:
            stocks = client.get_stock_list(market)
            print(f"✓ {market} 市场: 获取到 {len(stocks)} 只股票")
            if stocks:
                # 显示前3只股票
                sample_stocks = list(stocks.items())[:3]
                for code, name in sample_stocks:
                    print(f"  - {code}: {name}")
        except Exception as e:
            print(f"✗ 获取 {market} 市场股票列表失败: {str(e)}")
            return False
    
    # 测试股票代码验证
    print("\n4. 测试股票代码验证...")
    test_cases = [
        ("US", "AAPL", True),
        ("US", "INVALID", False),
        ("CN", "600519", True),
        ("CN", "12345", False),
        ("CRYPTO", "BTC/USDT", True),
        ("CRYPTO", "INVALID", False),
    ]
    
    for market, symbol, expected in test_cases:
        try:
            is_valid = client.validate_symbol(market, symbol)
            status = "✓" if is_valid == expected else "✗"
            print(f"{status} {market} {symbol}: {'有效' if is_valid else '无效'}")
        except Exception as e:
            print(f"✗ 验证 {market} {symbol} 失败: {str(e)}")
            return False
    
    # 测试获取价格数据（使用默认数据）
    print("\n5. 测试获取价格数据...")
    try:
        # 使用最近的日期
        end_date = time.strftime("%Y-%m-%d")
        start_date = time.strftime("%Y-%m-%d", time.localtime(time.time() - 7*24*3600))  # 7天前
        
        price_data = client.get_price_data("US", "AAPL", start_date, end_date)
        print(f"✓ 获取到 {len(price_data)} 条价格数据")
    except Exception as e:
        print(f"✗ 获取价格数据失败: {str(e)}")
        # 价格数据获取失败不影响整体测试
    
    # 测试获取公司信息
    print("\n6. 测试获取公司信息...")
    try:
        company_info = client.get_company_info("US", "AAPL")
        if company_info:
            print("✓ 获取公司信息成功")
        else:
            print("⚠ 公司信息为空")
    except Exception as e:
        print(f"✗ 获取公司信息失败: {str(e)}")
        # 公司信息获取失败不影响整体测试
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！API连接正常")
    print("=" * 50)
    return True


def test_fallback_mechanism():
    """测试降级机制"""
    print("\n" + "=" * 50)
    print("降级机制测试")
    print("=" * 50)
    
    # 使用错误的URL测试降级
    print("使用错误的API URL测试降级机制...")
    try:
        client = StockDataClient("http://invalid-url:9999")
        
        # 测试健康检查（应该返回False）
        is_healthy = client.health_check()
        print(f"✓ 健康检查结果: {'正常' if is_healthy else '异常'} (预期: 异常)")
        
        # 测试获取市场列表（应该返回默认数据）
        markets = client.get_available_markets()
        print(f"✓ 降级机制正常，获取到默认市场列表: {len(markets)} 个市场")
        
        # 测试获取股票列表（应该返回默认数据）
        stocks = client.get_stock_list("US")
        print(f"✓ 降级机制正常，获取到默认股票列表: {len(stocks)} 只股票")
        
    except Exception as e:
        print(f"✗ 降级机制测试失败: {str(e)}")
        return False
    
    print("✓ 降级机制测试通过")
    return True


def test_health_check_endpoints():
    """测试健康检查端点"""
    print("\n" + "=" * 50)
    print("健康检查端点测试")
    print("=" * 50)
    
    # 测试正确的URL
    print("测试正确的API URL...")
    try:
        client = StockDataClient("http://localhost:8000")
        is_healthy = client.health_check()
        print(f"✓ 健康检查结果: {'正常' if is_healthy else '异常'}")
    except Exception as e:
        print(f"✗ 健康检查失败: {str(e)}")
    
    # 测试错误的URL
    print("\n测试错误的API URL...")
    try:
        client = StockDataClient("http://localhost:9999")
        is_healthy = client.health_check()
        print(f"✓ 健康检查结果: {'正常' if is_healthy else '异常'} (预期: 异常)")
    except Exception as e:
        print(f"✗ 健康检查失败: {str(e)}")
    
    print("✓ 健康检查端点测试完成")


def main():
    """主函数"""
    print("AI Hedge Fund - API连接测试")
    print("=" * 50)
    
    # 测试健康检查端点
    test_health_check_endpoints()
    
    # 测试API连接
    if not test_api_connection():
        print("\n✗ API连接测试失败")
        return 1
    
    # 测试降级机制
    if not test_fallback_mechanism():
        print("\n✗ 降级机制测试失败")
        return 1
    
    print("\n🎉 所有测试通过！系统可以正常运行")
    return 0


if __name__ == "__main__":
    main() 