"""
状态指示器组件
用于显示API连接状态和其他系统状态
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QPainter, QColor
from typing import Optional


class StatusIndicator(QWidget):
    """状态指示器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._status = "unknown"  # unknown, connected, disconnected, error
        
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)
        
        # 状态图标
        self.status_icon = QLabel()
        self.status_icon.setFixedSize(12, 12)
        layout.addWidget(self.status_icon)
        
        # 状态文本
        self.status_text = QLabel("未知")
        self.status_text.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.status_text)
        
        layout.addStretch()
        
        # 设置初始状态
        self.set_status("unknown")
        
    def set_status(self, status: str, message: str = ""):
        """设置状态
        
        Args:
            status: 状态类型 (unknown, connected, disconnected, error)
            message: 状态消息
        """
        self._status = status
        
        # 设置图标
        if status == "connected":
            self.status_icon.setPixmap(self._create_status_icon(QColor(0, 150, 0)))  # 绿色
            self.status_text.setText("已连接" if not message else message)
            self.status_text.setStyleSheet("color: #009600; font-size: 11px;")
        elif status == "disconnected":
            self.status_icon.setPixmap(self._create_status_icon(QColor(255, 140, 0)))  # 橙色
            self.status_text.setText("未连接" if not message else message)
            self.status_text.setStyleSheet("color: #ff8c00; font-size: 11px;")
        elif status == "error":
            self.status_icon.setPixmap(self._create_status_icon(QColor(220, 20, 60)))  # 红色
            self.status_text.setText("连接错误" if not message else message)
            self.status_text.setStyleSheet("color: #dc143c; font-size: 11px;")
        else:  # unknown
            self.status_icon.setPixmap(self._create_status_icon(QColor(128, 128, 128)))  # 灰色
            self.status_text.setText("未知状态" if not message else message)
            self.status_text.setStyleSheet("color: #808080; font-size: 11px;")
    
    def _create_status_icon(self, color: QColor) -> QPixmap:
        """创建状态图标
        
        Args:
            color: 图标颜色
            
        Returns:
            QPixmap: 状态图标
        """
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制圆形图标
        painter.setBrush(color)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(2, 2, 8, 8)
        
        painter.end()
        return pixmap
    
    def get_status(self) -> str:
        """获取当前状态"""
        return self._status 