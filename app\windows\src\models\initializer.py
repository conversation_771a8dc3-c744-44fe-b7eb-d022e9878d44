from PyQt6.QtCore import QObject, pyqtSignal, QThread
import sys
import os
from typing import Dict, List, Optional

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from api.stock_data_client import StockDataClient

class InitializationTask(QObject):
    """初始化任务基类"""
    progress = pyqtSignal(str)  # 进度信号
    finished = pyqtSignal()     # 完成信号
    error = pyqtSignal(str)     # 错误信号

    def run(self):
        """执行初始化任务"""
        pass

class StockDataInitializer(InitializationTask):
    """股票数据初始化器"""
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        super().__init__()
        self.api_client = StockDataClient(api_base_url)
        self.stock_data = {}

    def run(self):
        """执行股票数据初始化"""
        from PyQt6.QtWidgets import QApplication
        try:
            # 开始获取股票数据（API连接已在主控制器中验证）
            self.progress.emit("开始获取股票数据...")
            QApplication.processEvents()

            # 初始化美股数据
            self.progress.emit("正在加载美股股票列表...")
            QApplication.processEvents()
            self._init_us_stocks()
            us_count = len(self.stock_data.get("US", {}))
            self.progress.emit(f"美股股票列表加载完成 ({us_count} 只股票)")
            QApplication.processEvents()

            # 初始化A股数据
            self.progress.emit("正在加载A股股票列表...")
            QApplication.processEvents()
            self._init_cn_stocks()
            cn_count = len(self.stock_data.get("CN", {}))
            self.progress.emit(f"A股股票列表加载完成 ({cn_count} 只股票)")
            QApplication.processEvents()

            # 初始化加密货币数据
            self.progress.emit("正在加载加密货币列表...")
            QApplication.processEvents()
            self._init_crypto_stocks()
            crypto_count = len(self.stock_data.get("CRYPTO", {}))
            self.progress.emit(f"加密货币列表加载完成 ({crypto_count} 种货币)")
            QApplication.processEvents()

            self.progress.emit("正在合并全部股票数据...")
            QApplication.processEvents()
            total_count = us_count + cn_count + crypto_count
            self.progress.emit(f"股票数据初始化完成 (共 {total_count} 个标的)")
            QApplication.processEvents()
            self.finished.emit()
        except Exception as e:
            self.progress.emit(f"股票数据初始化失败: {str(e)}")
            QApplication.processEvents()
            self.error.emit(f"股票数据初始化失败: {str(e)}")

    def _init_us_stocks(self):
        """初始化美股数据"""
        self.stock_data["US"] = self.api_client.get_stock_list("US")

    def _init_cn_stocks(self):
        """初始化A股数据"""
        self.stock_data["CN"] = self.api_client.get_stock_list("CN")

    def _init_crypto_stocks(self):
        """初始化加密货币数据"""
        self.stock_data["CRYPTO"] = self.api_client.get_stock_list("CRYPTO")

    def get_stock_data(self) -> Dict[str, Dict[str, str]]:
        """获取股票数据"""
        return self.stock_data

class SystemInitializer(QObject):
    """系统初始化器"""
    progress = pyqtSignal(str)  # 进度信号
    finished = pyqtSignal()     # 完成信号
    error = pyqtSignal(str)     # 错误信号

    def __init__(self):
        super().__init__()
        self.tasks = []
        self.results = {}

    def add_task(self, task: InitializationTask, name: str):
        """添加初始化任务"""
        self.tasks.append((name, task))

    def run(self):
        """执行所有初始化任务"""
        from PyQt6.QtWidgets import QApplication
        try:
            self.progress.emit("系统初始化开始...")
            QApplication.processEvents()

            total_tasks = len(self.tasks)
            for i, (name, task) in enumerate(self.tasks, 1):
                self.progress.emit(f"正在执行初始化任务: {name} ({i}/{total_tasks})")
                QApplication.processEvents()
                task.progress.connect(self.progress.emit)
                task.run()
                # 保存任务结果
                if hasattr(task, 'get_stock_data'):
                    self.results[name] = task.get_stock_data()
                self.progress.emit(f"{name} 初始化完成 ({i}/{total_tasks})")
                QApplication.processEvents()

            self.progress.emit("所有初始化任务已完成，系统初始化完成")
            QApplication.processEvents()
            self.finished.emit()
        except Exception as e:
            self.error.emit(f"系统初始化失败: {str(e)}")
            QApplication.processEvents()

    def get_result(self, task_name: str):
        """获取指定任务的结果"""
        return self.results.get(task_name)

class ApiCheckWorker(QObject):
    finished = pyqtSignal(bool)
    def __init__(self, api_base_url):
        super().__init__()
        self.api_base_url = api_base_url

    def run(self):
        from api.client import APIClient
        # 创建无重试的客户端，快速失败
        client = APIClient(self.api_base_url, max_retries=1)
        # 为health_check设置3秒的timeout
        result = client.health_check()
        self.finished.emit(result) 