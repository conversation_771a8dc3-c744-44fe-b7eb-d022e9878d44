"""
API日志功能使用示例
演示如何在实际项目中使用API调试日志功能
"""

from api.client import APIClient
from api.hedge_fund_client import HedgeFundClient
from api.logger_config import (
    enable_api_debug,
    disable_all_api_logging,
    enable_minimal_api_logging,
    enable_full_api_logging,
    get_api_log_directory,
    clear_api_logs,
    logger_config
)


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 1. 创建带调试的API客户端
    client = APIClient("http://localhost:8000", debug=True)
    
    # 2. 执行API调用（会自动记录日志）
    try:
        result = client.health_check()
        print(f"健康检查结果: {result}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 3. 关闭客户端
    client.close()


def example_hedge_fund_client():
    """对冲基金客户端示例"""
    print("\n=== 对冲基金客户端示例 ===")
    
    # 创建对冲基金客户端
    hf_client = HedgeFundClient("http://localhost:8000", debug=True)
    
    try:
        # 获取可用的AI代理
        agents = hf_client.get_agents()
        print(f"可用代理数量: {len(agents.get('agents', []))}")
        
        # 执行分析（示例）
        result = hf_client.run_hedge_fund_sync(
            tickers=["AAPL"],
            selected_agents=["warren_buffett"],
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        print(f"分析完成: {type(result)}")
        
    except Exception as e:
        print(f"分析失败: {e}")
    
    hf_client.close()


def example_log_control():
    """日志控制示例"""
    print("\n=== 日志控制示例 ===")
    
    # 1. 启用完整调试日志
    print("1. 启用完整调试日志")
    enable_full_api_logging()
    
    client = APIClient("http://localhost:8000")
    client.health_check()  # 会产生详细日志
    client.close()
    
    # 2. 切换到最小日志模式
    print("\n2. 切换到最小日志模式")
    enable_minimal_api_logging()
    
    client = APIClient("http://localhost:8000")
    client.health_check()  # 只记录错误
    client.close()
    
    # 3. 完全关闭日志
    print("\n3. 完全关闭日志")
    disable_all_api_logging()
    
    client = APIClient("http://localhost:8000")
    client.health_check()  # 无日志输出
    client.close()
    
    # 4. 重新启用日志
    print("\n4. 重新启用日志")
    enable_api_debug(True)


def example_log_management():
    """日志管理示例"""
    print("\n=== 日志管理示例 ===")
    
    # 1. 查看日志目录
    log_dir = get_api_log_directory()
    print(f"日志目录: {log_dir}")
    
    # 2. 查看日志文件
    log_files = logger_config.get_log_files()
    print(f"日志文件数量: {len(log_files)}")
    for log_file in log_files:
        print(f"  {log_file['name']}: {log_file['size_mb']} MB")
    
    # 3. 自定义日志配置
    print("\n自定义日志配置:")
    logger_config.set_file_size_limit(20)  # 20MB
    logger_config.set_max_files(10)        # 10个文件
    logger_config.set_cleanup_days(14)     # 14天清理
    
    config = logger_config.get_config()
    print(f"文件大小限制: {config.get('max_file_size_mb')} MB")
    print(f"最大文件数: {config.get('max_files')}")
    print(f"清理天数: {config.get('cleanup_days')}")


def example_production_setup():
    """生产环境设置示例"""
    print("\n=== 生产环境设置示例 ===")
    
    # 生产环境推荐设置
    print("生产环境推荐设置:")
    
    # 1. 只记录错误信息
    enable_minimal_api_logging()
    
    # 2. 关闭控制台输出，只保留文件日志
    logger_config.enable_console(False)
    logger_config.enable_file_logging(True)
    
    # 3. 设置较小的文件大小和数量
    logger_config.set_file_size_limit(5)   # 5MB
    logger_config.set_max_files(3)         # 3个文件
    logger_config.set_cleanup_days(3)      # 3天清理
    
    print("✅ 生产环境日志配置完成")
    
    # 测试生产环境设置
    client = APIClient("http://localhost:8000")
    try:
        client.health_check()
        print("✅ 生产环境测试成功")
    except Exception as e:
        print(f"❌ 生产环境测试失败: {e}")
    client.close()


def example_development_setup():
    """开发环境设置示例"""
    print("\n=== 开发环境设置示例 ===")
    
    # 开发环境推荐设置
    print("开发环境推荐设置:")
    
    # 1. 启用完整调试日志
    enable_full_api_logging()
    
    # 2. 启用控制台和文件双重输出
    logger_config.enable_console(True)
    logger_config.enable_file_logging(True)
    
    # 3. 设置较大的文件大小和数量
    logger_config.set_file_size_limit(50)  # 50MB
    logger_config.set_max_files(10)        # 10个文件
    logger_config.set_cleanup_days(7)      # 7天清理
    
    print("✅ 开发环境日志配置完成")
    
    # 测试开发环境设置
    client = APIClient("http://localhost:8000")
    try:
        client.health_check()
        print("✅ 开发环境测试成功")
    except Exception as e:
        print(f"❌ 开发环境测试失败: {e}")
    client.close()


def example_context_manager():
    """上下文管理器使用示例"""
    print("\n=== 上下文管理器使用示例 ===")
    
    # 使用with语句自动管理客户端生命周期
    with APIClient("http://localhost:8000", debug=True) as client:
        try:
            result = client.health_check()
            print(f"健康检查结果: {result}")
        except Exception as e:
            print(f"请求失败: {e}")
    # 客户端会自动关闭
    
    # 对冲基金客户端也支持上下文管理器
    with HedgeFundClient("http://localhost:8000", debug=True) as hf_client:
        try:
            agents = hf_client.get_agents()
            print(f"获取到 {len(agents.get('agents', []))} 个代理")
        except Exception as e:
            print(f"获取代理失败: {e}")


def example_streaming_with_logging():
    """流式请求日志示例"""
    print("\n=== 流式请求日志示例 ===")
    
    def progress_callback(data):
        print(f"📊 进度更新: {data.get('type', 'unknown')}")
    
    def complete_callback(data):
        print(f"✅ 分析完成: {len(data) if isinstance(data, dict) else type(data)}")
    
    def error_callback(error):
        print(f"❌ 分析错误: {error}")
    
    with HedgeFundClient("http://localhost:8000", debug=True) as hf_client:
        try:
            result = hf_client.run_hedge_fund_streaming(
                tickers=["AAPL", "GOOGL"],
                selected_agents=["warren_buffett", "peter_lynch"],
                start_date="2024-01-01",
                end_date="2024-01-31",
                progress_callback=progress_callback,
                complete_callback=complete_callback,
                error_callback=error_callback
            )
            print(f"流式分析结果: {type(result)}")
        except Exception as e:
            print(f"流式分析失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("API日志功能使用示例")
    print("=" * 50)
    
    try:
        # 基础使用
        example_basic_usage()
        
        # 对冲基金客户端
        example_hedge_fund_client()
        
        # 日志控制
        example_log_control()
        
        # 日志管理
        example_log_management()
        
        # 生产环境设置
        example_production_setup()
        
        # 开发环境设置
        example_development_setup()
        
        # 上下文管理器
        example_context_manager()
        
        # 流式请求
        example_streaming_with_logging()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        print(f"\n📁 日志文件位置: {get_api_log_directory()}")
        
        # 显示最终配置
        print("\n📋 当前配置:")
        config = logger_config.get_config()
        for key, value in config.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
