import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入模块化组件
from views.ui.menu_bar import MenuBar
from views.ui.toolbar import RibbonToolBar
from views.ui.navigation_tree import NavigationTree
from views.ui.status_bar import StatusBar
from views.ui.loading_overlay import LoadingOverlay
from views.ui.status_indicator import StatusIndicator
from views.common.page_manager import PageManager
from controllers.main_controller import MainController
from styles import get_style


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI量化交易系统")
        self.setMinimumSize(1200, 800)
        
        # 初始化控制器
        self.controller = MainController(self)
        
        # 添加标志来控制是否显示关闭确认对话框
        self._force_close = False
        
        # 初始化UI组件
        self._init_ui()
        self._init_components()
        self._connect_signals()
        
        # 应用初始样式
        self._apply_styles()
        
        # 先显示界面，再异步初始化系统
        self.show()
        QTimer.singleShot(0, self.controller.start_initialization)
        
    def _init_ui(self):
        """初始化UI"""
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 创建左侧树形导航
        self.navigation_tree = NavigationTree()
        splitter.addWidget(self.navigation_tree)
        
        # 创建右侧堆叠窗口
        self.page_manager = PageManager()
        splitter.addWidget(self.page_manager)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 4)
        
        # 创建加载遮罩层
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.show()
        self.loading_overlay.raise_()
        self.loading_overlay.show_message("正在启动系统...")
        # self.loading_overlay.update_status("正在检查API连接...")
        
    def _init_components(self):
        """初始化组件"""
        # 设置菜单栏
        self.menu_bar = MenuBar(self)
        self.setMenuBar(self.menu_bar)
        
        # 设置工具栏
        self.toolbar = RibbonToolBar(self)
        self.addToolBar(self.toolbar)
        
        # 设置状态栏
        self.status_bar = StatusBar(self)
        self.setStatusBar(self.status_bar)
        
        # 创建状态指示器
        self.status_indicator = StatusIndicator()
        # 将状态指示器添加到状态栏
        self.status_bar.addPermanentWidget(self.status_indicator)
        
    def _connect_signals(self):
        """连接信号"""
        # 菜单栏信号
        self.menu_bar.new_triggered.connect(self.controller.handle_new)
        self.menu_bar.open_triggered.connect(self.controller.handle_open)
        self.menu_bar.save_triggered.connect(self.controller.handle_save)
        self.menu_bar.settings_triggered.connect(self.controller.handle_settings)
        self.menu_bar.about_triggered.connect(self.controller.handle_about)
        self.menu_bar.toolbar_toggled.connect(self.toolbar.setVisible)
        self.menu_bar.statusbar_toggled.connect(self.status_bar.setVisible)
        
        # 工具栏信号
        self.toolbar.new_clicked.connect(self.controller.handle_new)
        self.toolbar.open_clicked.connect(self.controller.handle_open)
        self.toolbar.save_clicked.connect(self.controller.handle_save)
        self.toolbar.settings_clicked.connect(self.controller.handle_settings)
        self.toolbar.help_clicked.connect(self.controller.handle_about)
        
        # 导航树信号
        self.navigation_tree.item_clicked.connect(self.controller.handle_tree_item_clicked)
        
        # 页面管理器信号
        self.page_manager.page_changed.connect(self._on_page_changed)
        
        # 主题切换信号
        theme_switcher = self.page_manager.get_theme_switcher()
        if theme_switcher:
            theme_switcher.theme_changed.connect(self.controller.handle_theme_change)
            
        # LLM配置信号
        llm_config_view = self.page_manager.get_llm_config_view()
        if llm_config_view:
            llm_config_view.config_changed.connect(self._on_llm_config_changed)
            
        # 连接单股单顾问页面的信号
        self._connect_single_stock_single_agent_signals()
        
    def _connect_single_stock_single_agent_signals(self):
        """连接单股单顾问页面的信号"""
        page = self.page_manager.page_single_stock_single_agent
        
        # 股票选择信号
        if hasattr(page, 'stock_input'):
            page.stock_input.stocks_changed.connect(self.controller.handle_stock_selection)
            
        # 日期范围信号
        if hasattr(page, 'date_range'):
            page.date_range.date_range_changed.connect(self.controller.handle_date_range)
            
        # Agent选择信号
        if hasattr(page, 'agent_selector'):
            page.agent_selector.agents_changed.connect(self.controller.handle_agent_selection)
            
        # 开始分析按钮信号 - 已在页面内部处理，无需重复连接
        # if hasattr(page, 'start_analyze_btn'):
        #     page.start_analyze_btn.clicked.connect(self.controller.handle_start_analyze)
            
        # 对话发送信号
        if hasattr(page, 'chat_send_btn'):
            page.chat_send_btn.clicked.connect(self.controller.handle_chat_send)
        if hasattr(page, 'chat_input'):
            page.chat_input.returnPressed.connect(self.controller.handle_chat_send)
            
    def _on_page_changed(self, index: int):
        """页面变化处理"""
        # 可以在这里添加页面切换时的逻辑
        pass
        
    def _on_llm_config_changed(self, config):
        """LLM配置变化处理"""
        print(f"LLM配置变化: {config}")
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(get_style("main_window"))
        
    def get_style(self, style_name: str):
        """获取样式"""
        return get_style(style_name)
        
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.loading_overlay:
            self.loading_overlay.setGeometry(0, 0, self.width(), self.height())
            
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 清理资源
        self.controller.cleanup()
        
        # 如果强制关闭标志为真，则直接关闭程序
        if self._force_close:
            event.accept()
        else:
            # 显示确认对话框
            reply = QMessageBox.question(
                self, "确认退出",
                "确定要退出程序吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                event.accept()
            else:
                event.ignore()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 