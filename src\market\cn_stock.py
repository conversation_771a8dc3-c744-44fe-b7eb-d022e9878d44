from typing import List, Dict, Optional
from datetime import datetime
import os
import requests
import akshare as ak
from .base import MarketProvider, MarketData, MarketInfo

class CNStockMarketProvider(MarketProvider):
    """A股市场数据提供者"""
    
    def __init__(self):
        # 可以添加A股特定的API密钥
        self.api_key = os.environ.get("CN_STOCK_API_KEY")
        self.headers = {"X-API-KEY": self.api_key} if self.api_key else {}
        
    def get_market_info(self) -> MarketInfo:
        return MarketInfo(
            name="China A-Share Market",
            code="CN",
            currency="CNY",
            timezone="Asia/Shanghai",
            trading_hours={
                "morning": ["09:30", "11:30"],
                "afternoon": ["13:00", "15:00"]
            },
            trading_days=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
        )
    
    def get_price_data(self, symbol: str, start_date: str, end_date: str) -> List[MarketData]:
        # 使用akshare获取A股数据
        try:
            # 转换日期格式
            start = datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y%m%d")
            end = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y%m%d")
            
            # 获取股票数据
            df = ak.stock_zh_a_hist(symbol=symbol, start_date=start, end_date=end)
            
            return [
                MarketData(
                    open=float(row["开盘"]),
                    close=float(row["收盘"]),
                    high=float(row["最高"]),
                    low=float(row["最低"]),
                    volume=float(row["成交量"]),
                    time=row["日期"],
                    currency="CNY",
                    exchange="SSE/SZSE"
                )
                for _, row in df.iterrows()
            ]
        except Exception as e:
            print(f"Error fetching A-share data: {e}")
            return []
    
    def get_financial_metrics(self, symbol: str, end_date: str) -> Dict:
        try:
            # 使用akshare获取财务指标
            df = ak.stock_financial_analysis_indicator(symbol)
            if df.empty:
                return {}
            
            # 转换日期格式
            target_date = datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m-%d")
            
            # 获取最接近目标日期的数据
            df["日期"] = pd.to_datetime(df["日期"])
            closest_date = df[df["日期"] <= target_date]["日期"].max()
            metrics = df[df["日期"] == closest_date].iloc[0].to_dict()
            
            return metrics
        except Exception as e:
            print(f"Error fetching financial metrics: {e}")
            return {}
    
    def get_company_info(self, symbol: str) -> Dict:
        try:
            # 使用akshare获取公司基本信息
            info = ak.stock_individual_info_em(symbol)
            return info.to_dict()
        except Exception as e:
            print(f"Error fetching company info: {e}")
            return {}
    
    def get_news(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        try:
            # 使用akshare获取公司新闻
            news = ak.stock_news_em(symbol)
            if start_date and end_date:
                news["日期"] = pd.to_datetime(news["日期"])
                mask = (news["日期"] >= start_date) & (news["日期"] <= end_date)
                news = news[mask]
            return news.to_dict("records")
        except Exception as e:
            print(f"Error fetching news: {e}")
            return []
    
    def get_insider_trades(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict]:
        try:
            # 使用akshare获取股东增减持数据
            trades = ak.stock_share_hold_change_em(symbol)
            if start_date and end_date:
                trades["变动日期"] = pd.to_datetime(trades["变动日期"])
                mask = (trades["变动日期"] >= start_date) & (trades["变动日期"] <= end_date)
                trades = trades[mask]
            return trades.to_dict("records")
        except Exception as e:
            print(f"Error fetching insider trades: {e}")
            return []
    
    def validate_symbol(self, symbol: str) -> bool:
        # 验证A股代码格式
        if not symbol:
            return False
        # A股代码为6位数字
        return bool(symbol.isdigit() and len(symbol) == 6)
    
    def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        try:
            # 使用akshare获取交易日历
            calendar = ak.tool_trade_date_hist_sina()
            mask = (calendar["trade_date"] >= start_date) & (calendar["trade_date"] <= end_date)
            trading_days = calendar[mask]["trade_date"].tolist()
            return trading_days
        except Exception as e:
            print(f"Error fetching trading days: {e}")
            # 返回一个简单的实现
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")
            days = []
            current = start
            while current <= end:
                if current.weekday() < 5:  # 0-4 表示周一到周五
                    days.append(current.strftime("%Y-%m-%d"))
                current = datetime.fromtimestamp(current.timestamp() + 86400)
            return days 