#!/usr/bin/env python3
"""
样式测试脚本 - Windows WinForm风格
用于验证统一的样式定义和主题切换功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt6.QtWidgets import QLabel, QPushButton, QComboBox, QDateEdit, QLineEdit, QTabWidget
from PyQt6.QtCore import QDate
from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QScrollArea, QFrame

# 添加src目录到路径
sys.path.append('src')

from src.styles import ThemeManager, Theme, get_style
from src.views.theme_switcher import ThemeSwitcher

class StyleTestWindow(QMainWindow):
    """样式测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Windows WinForm风格测试")
        self.setMinimumSize(1000, 700)
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        
        # 主题切换器
        self.theme_switcher = ThemeSwitcher()
        self.theme_switcher.theme_changed.connect(self._on_theme_changed)
        layout.addWidget(self.theme_switcher)
        
        # 标题
        title = QLabel("Windows WinForm风格测试页面")
        title.setStyleSheet(get_style("title"))
        layout.addWidget(title)
        
        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(get_style("tab"))
        
        # 基础组件测试页
        basic_tab = self._create_basic_components_tab()
        tab_widget.addTab(basic_tab, "基础组件")
        
        # 按钮测试页
        button_tab = self._create_button_test_tab()
        tab_widget.addTab(button_tab, "按钮样式")
        
        # 表格测试页
        table_tab = self._create_table_test_tab()
        tab_widget.addTab(table_tab, "表格样式")
        
        layout.addWidget(tab_widget)
        
        # 应用初始样式
        self._apply_styles()
        
    def _create_basic_components_tab(self):
        """创建基础组件测试页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 标签样式测试
        label_section = QLabel("标签样式:")
        label_section.setStyleSheet(get_style("label") + "font-weight: bold;")
        layout.addWidget(label_section)
        
        test_label = QLabel("这是一个测试标签")
        test_label.setStyleSheet(get_style("label"))
        layout.addWidget(test_label)
        
        # 输入控件样式测试
        input_section = QLabel("输入控件样式:")
        input_section.setStyleSheet(get_style("label") + "font-weight: bold;")
        layout.addWidget(input_section)
        
        # 下拉框
        combo = QComboBox()
        combo.addItems(["选项1", "选项2", "选项3"])
        combo.setStyleSheet(get_style("combobox"))
        layout.addWidget(combo)
        
        # 日期编辑框
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setStyleSheet(get_style("dateedit"))
        layout.addWidget(date_edit)
        
        # 文本输入框
        text_input = QLineEdit()
        text_input.setPlaceholderText("请输入文本")
        text_input.setStyleSheet(get_style("input"))
        layout.addWidget(text_input)
        
        # 卡片样式测试
        card_section = QLabel("卡片样式:")
        card_section.setStyleSheet(get_style("label") + "font-weight: bold;")
        layout.addWidget(card_section)
        
        card = QFrame()
        card.setStyleSheet(get_style("card"))
        card_layout = QVBoxLayout(card)
        card_layout.addWidget(QLabel("这是一个卡片组件"))
        card_layout.addWidget(QLabel("可以包含多个子组件"))
        layout.addWidget(card)
        
        layout.addStretch()
        return tab
        
    def _create_button_test_tab(self):
        """创建按钮测试页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 按钮样式测试
        button_section = QLabel("按钮样式:")
        button_section.setStyleSheet(get_style("label") + "font-weight: bold;")
        layout.addWidget(button_section)
        
        # 按钮行1
        button_layout1 = QHBoxLayout()
        
        primary_btn = QPushButton("主要按钮")
        primary_btn.setStyleSheet(get_style("button_primary"))
        button_layout1.addWidget(primary_btn)
        
        secondary_btn = QPushButton("次要按钮")
        secondary_btn.setStyleSheet(get_style("button_secondary"))
        button_layout1.addWidget(secondary_btn)
        
        layout.addLayout(button_layout1)
        
        # 按钮行2
        button_layout2 = QHBoxLayout()
        
        success_btn = QPushButton("成功按钮")
        success_btn.setStyleSheet(get_style("button_success"))
        button_layout2.addWidget(success_btn)
        
        danger_btn = QPushButton("危险按钮")
        danger_btn.setStyleSheet(get_style("button_danger"))
        button_layout2.addWidget(danger_btn)
        
        layout.addLayout(button_layout2)
        
        # 开始分析按钮
        analyze_btn = QPushButton("开始分析")
        analyze_btn.setStyleSheet(get_style("start_analyze_button"))
        layout.addWidget(analyze_btn)
        
        layout.addStretch()
        return tab
        
    def _create_table_test_tab(self):
        """创建表格测试页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # 表格样式测试
        table_section = QLabel("表格样式:")
        table_section.setStyleSheet(get_style("label") + "font-weight: bold;")
        layout.addWidget(table_section)
        
        # 创建表格
        table = QTableWidget()
        table.setStyleSheet(get_style("table"))
        table.setColumnCount(4)
        table.setRowCount(5)
        table.setHorizontalHeaderLabels(["股票代码", "股票名称", "当前价格", "涨跌幅"])
        
        # 添加测试数据
        test_data = [
            ["AAPL", "Apple Inc.", "$150.25", "+2.5%"],
            ["MSFT", "Microsoft Corporation", "$320.10", "+1.8%"],
            ["GOOGL", "Alphabet Inc.", "$2800.50", "-0.5%"],
            ["AMZN", "Amazon.com Inc.", "$3300.75", "+3.2%"],
            ["TSLA", "Tesla Inc.", "$850.30", "+5.1%"]
        ]
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                table.setItem(row, col, item)
        
        layout.addWidget(table)
        layout.addStretch()
        return tab
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(get_style("main_window"))
        
    def _on_theme_changed(self, theme):
        """主题变化处理"""
        # 刷新主题切换器样式
        self.theme_switcher.refresh_styles()
        
        # 刷新主窗口样式
        self._apply_styles()
        
        # 刷新所有子组件样式
        self._refresh_all_styles()
        
        print(f"主题已切换到: {theme}")
        
    def _refresh_all_styles(self):
        """刷新所有样式"""
        # 刷新所有标签
        for child in self.findChildren(QLabel):
            if "font-weight: bold" in child.styleSheet():
                child.setStyleSheet(get_style("label") + "font-weight: bold;")
            else:
                child.setStyleSheet(get_style("label"))
                
        # 刷新所有下拉框
        for child in self.findChildren(QComboBox):
            child.setStyleSheet(get_style("combobox"))
            
        # 刷新所有日期编辑框
        for child in self.findChildren(QDateEdit):
            child.setStyleSheet(get_style("dateedit"))
            
        # 刷新所有输入框
        for child in self.findChildren(QLineEdit):
            child.setStyleSheet(get_style("input"))
            
        # 刷新所有按钮
        for child in self.findChildren(QPushButton):
            if "开始分析" in child.text():
                child.setStyleSheet(get_style("start_analyze_button"))
            elif "主要" in child.text():
                child.setStyleSheet(get_style("button_primary"))
            elif "次要" in child.text():
                child.setStyleSheet(get_style("button_secondary"))
            elif "成功" in child.text():
                child.setStyleSheet(get_style("button_success"))
            elif "危险" in child.text():
                child.setStyleSheet(get_style("button_danger"))
            else:
                child.setStyleSheet(get_style("button_primary"))
                
        # 刷新所有表格
        for child in self.findChildren(QTableWidget):
            child.setStyleSheet(get_style("table"))
            
        # 刷新所有标签页
        for child in self.findChildren(QTabWidget):
            child.setStyleSheet(get_style("tab"))
            
        # 刷新所有卡片
        for child in self.findChildren(QFrame):
            if "card" in child.styleSheet():
                child.setStyleSheet(get_style("card"))

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = StyleTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 