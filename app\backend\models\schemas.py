from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from src.llm.models import ModelProvider


class AgentModelConfig(BaseModel):
    agent_id: str
    model_name: Optional[str] = None
    model_provider: Optional[ModelProvider] = None


class HedgeFundResponse(BaseModel):
    decisions: dict
    analyst_signals: dict


class ErrorResponse(BaseModel):
    message: str
    error: str | None = None


class HedgeFundRequest(BaseModel):
    tickers: List[str]
    selected_agents: List[str]
    agent_models: Optional[List[AgentModelConfig]] = None
    end_date: Optional[str] = Field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d"))
    start_date: Optional[str] = None
    model_name: str = "gpt-4o"
    model_provider: ModelProvider = ModelProvider.OPENAI
    initial_cash: float = 100000.0
    margin_requirement: float = 0.0

    def get_start_date(self) -> str:
        """Calculate start date if not provided"""
        if self.start_date:
            return self.start_date
        return (datetime.strptime(self.end_date, "%Y-%m-%d") - timedelta(days=90)).strftime("%Y-%m-%d")

    def get_agent_model_config(self, agent_id: str) -> tuple[str, ModelProvider]:
        """Get model configuration for a specific agent"""
        if self.agent_models:
            for config in self.agent_models:
                if config.agent_id == agent_id:
                    return (
                        config.model_name or self.model_name,
                        config.model_provider or self.model_provider
                    )
        # Fallback to global model settings
        return self.model_name, self.model_provider

# 股票数据相关模型
class StockListResponse(BaseModel):
    """股票列表响应模型"""
    market: str
    stocks: Dict[str, str]  # 股票代码到股票名称的映射
    count: int

class PriceDataResponse(BaseModel):
    """价格数据响应模型"""
    market: str
    symbol: str
    start_date: str
    end_date: str
    data: List[Dict[str, Any]]  # 价格数据列表
    count: int

class FinancialMetricsResponse(BaseModel):
    """财务指标响应模型"""
    market: str
    symbol: str
    end_date: str
    metrics: Dict[str, Any]

class CompanyInfoResponse(BaseModel):
    """公司信息响应模型"""
    market: str
    symbol: str
    info: Dict[str, Any]

class NewsResponse(BaseModel):
    """新闻数据响应模型"""
    market: str
    symbol: str
    start_date: Optional[str]
    end_date: Optional[str]
    news: List[Dict[str, Any]]
    count: int

class InsiderTradesResponse(BaseModel):
    """内部交易响应模型"""
    market: str
    symbol: str
    start_date: Optional[str]
    end_date: Optional[str]
    trades: List[Dict[str, Any]]
    count: int

class MarketInfoResponse(BaseModel):
    """市场信息响应模型"""
    market: str
    info: Dict[str, Any]
