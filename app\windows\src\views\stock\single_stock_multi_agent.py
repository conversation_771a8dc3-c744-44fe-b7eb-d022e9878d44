import sys
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
)
from .stock_input import StockInputView
from .date_range import DateRangeView
from ..common.results_view import ResultsView
from ..common.agent_selector import AgentSelector
from styles import get_style

class SingleStockMultiAgentPage(QWidget):
    """单股多顾问对比页面"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        # 页面说明
        page_desc = QLabel("🔍 单股多顾问对比分析：选择多个AI投资顾问对同一只股票进行分析，获得不同投资大师的观点对比，提高分析权威性。")
        page_desc.setStyleSheet(get_style("page_description"))
        page_desc.setWordWrap(True)
        layout.addWidget(page_desc)
        # 第一行：股票选择、多Agent选择和日期选择
        top_layout = QHBoxLayout()
        # 股票输入组件（单选）
        stock_group = QWidget()
        stock_layout = QVBoxLayout(stock_group)
        stock_title = QLabel("股票选择")
        stock_title.setStyleSheet(get_style("title"))
        stock_layout.addWidget(stock_title)
        self.stock_input_single_multi = StockInputView()
        stock_layout.addWidget(self.stock_input_single_multi)
        top_layout.addWidget(stock_group)
        # 多Agent选择组件
        agent_group = QWidget()
        agent_layout = QVBoxLayout(agent_group)
        agent_tips = QLabel("💡 选择多个AI投资顾问进行横向对比，提高分析权威性")
        agent_tips.setStyleSheet(get_style("tips"))
        agent_tips.setWordWrap(True)
        agent_layout.addWidget(agent_tips)
        self.agent_selector_single_multi = AgentSelector(multi_select=True)
        agent_layout.addWidget(self.agent_selector_single_multi)
        top_layout.addWidget(agent_group)
        # 日期范围组件
        date_group = QWidget()
        date_layout = QVBoxLayout(date_group)
        date_title = QLabel("时间范围")
        date_title.setStyleSheet(get_style("title"))
        date_layout.addWidget(date_title)
        self.date_range_single_multi = DateRangeView()
        date_layout.addWidget(self.date_range_single_multi)
        top_layout.addWidget(date_group)
        layout.addLayout(top_layout)
        # 第二行：开始分析按钮
        analyze_layout = QHBoxLayout()
        analyze_layout.addStretch()
        self.start_analyze_single_multi_btn = QPushButton("开始多顾问对比分析")
        self.start_analyze_single_multi_btn.setStyleSheet(get_style("start_analyze_button"))
        analyze_layout.addWidget(self.start_analyze_single_multi_btn)
        analyze_layout.addStretch()
        layout.addLayout(analyze_layout)
        # 第三行：结果显示组件
        self.results_view_single_multi = ResultsView()
        layout.addWidget(self.results_view_single_multi) 