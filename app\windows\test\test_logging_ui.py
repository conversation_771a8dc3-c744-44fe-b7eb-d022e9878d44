#!/usr/bin/env python3
"""
测试日志配置UI组件
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加src目录到Python路径
src_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    from PyQt6.QtCore import Qt
    
    from views.settings.logging_config import LoggingConfigView
    from utils.config_manager import get_config_manager
    from api.logger_config import logger_config

    class LoggingConfigTestWindow(QMainWindow):
        """日志配置测试窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("日志配置测试")
            self.setGeometry(100, 100, 800, 600)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 创建日志配置视图
            self.logging_config_view = LoggingConfigView()
            layout.addWidget(self.logging_config_view)
            
            # 连接信号
            self.logging_config_view.config_changed.connect(self._on_config_changed)
            
            print("日志配置UI测试窗口已创建")
            print(f"ConfigManager集成: {'✅' if logger_config.use_config_manager else '❌'}")
        
        def _on_config_changed(self, config):
            """配置变化处理"""
            print(f"配置已变化: {config}")

    def test_ui_components():
        """测试UI组件"""
        print("=== 测试日志配置UI组件 ===")
        
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = LoggingConfigTestWindow()
        window.show()
        
        print("UI测试窗口已显示")
        print("请在窗口中测试以下功能:")
        print("1. 基础设置开关")
        print("2. 文件管理参数")
        print("3. 快速操作按钮")
        print("4. 日志文件信息显示")
        print("5. 保存和应用按钮")
        print("\n关闭窗口以继续...")
        
        # 运行应用
        app.exec()
        
        print("UI测试完成")

    def test_config_integration():
        """测试配置集成"""
        print("\n=== 测试配置集成 ===")
        
        config_manager = get_config_manager()
        
        # 测试初始配置
        print("1. 初始配置:")
        initial_config = config_manager.get_logging_config()
        print(f"   {initial_config}")
        
        # 测试配置更新
        print("\n2. 测试配置更新:")
        test_config = {
            "debug_enabled": False,
            "console_enabled": True,
            "file_enabled": False,
            "max_file_size_mb": 25,
            "max_files": 8,
            "cleanup_days": 12
        }
        
        success = config_manager.set_logging_config(test_config)
        print(f"   更新结果: {'✅' if success else '❌'}")
        
        # 验证更新
        updated_config = config_manager.get_logging_config()
        matches = all(updated_config.get(k) == v for k, v in test_config.items())
        print(f"   配置匹配: {'✅' if matches else '❌'}")
        
        # 恢复初始配置
        config_manager.set_logging_config(initial_config)
        print("   已恢复初始配置")

    def test_logger_integration():
        """测试日志器集成"""
        print("\n=== 测试日志器集成 ===")
        
        # 测试日志器配置应用
        print("1. 测试配置应用到日志器:")
        
        # 启用完整日志
        logger_config.enable_full_logging()
        config = logger_config.get_config()
        print(f"   完整日志配置: {config}")
        
        # 测试API调用
        from api.client import APIClient
        client = APIClient("http://localhost:8000", debug=True)
        
        print("\n2. 测试API调用日志:")
        try:
            result = client.health_check()
            print(f"   API调用结果: {'✅' if result else '❌'}")
        except Exception as e:
            print(f"   API调用异常: {e}")
        
        # 测试配置变更
        print("\n3. 测试配置变更:")
        logger_config.enable_minimal_logging()
        print("   已切换到最小日志模式")
        
        try:
            client.health_check()
            print("   最小日志模式API调用: ✅")
        except Exception as e:
            print(f"   最小日志模式API调用异常: {e}")
        
        # 恢复完整日志
        logger_config.enable_full_logging()
        client.close()

    def test_file_operations():
        """测试文件操作"""
        print("\n=== 测试文件操作 ===")
        
        # 测试日志目录
        log_dir = logger_config.get_log_directory()
        print(f"1. 日志目录: {log_dir}")
        print(f"   目录存在: {'✅' if os.path.exists(log_dir) else '❌'}")
        
        # 测试日志文件列表
        log_files = logger_config.get_log_files()
        print(f"\n2. 日志文件: {len(log_files)} 个")
        for log_file in log_files:
            print(f"   📄 {log_file['name']}: {log_file['size_mb']:.2f} MB")
        
        # 测试配置文件
        config_manager = get_config_manager()
        config_file = config_manager.get_config_file_path()
        print(f"\n3. 配置文件: {config_file}")
        print(f"   文件存在: {'✅' if os.path.exists(config_file) else '❌'}")

    def main():
        """主测试函数"""
        print("日志配置UI集成测试")
        print("=" * 50)
        
        try:
            # 配置集成测试
            test_config_integration()
            
            # 日志器集成测试
            test_logger_integration()
            
            # 文件操作测试
            test_file_operations()
            
            # UI组件测试（需要用户交互）
            print("\n" + "=" * 50)
            print("准备启动UI测试...")
            input("按Enter键继续UI测试，或Ctrl+C取消...")
            test_ui_components()
            
            print("\n" + "=" * 50)
            print("✅ 所有测试完成！")
            print("\n集成成果:")
            print("1. ✅ 日志配置UI组件正常工作")
            print("2. ✅ 与ConfigManager完全集成")
            print("3. ✅ 配置变更实时生效")
            print("4. ✅ 文件操作功能正常")
            print("5. ✅ API日志功能集成成功")
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt6和相关依赖")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
