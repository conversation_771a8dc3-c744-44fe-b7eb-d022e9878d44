from PyQt6.QtWidgets import QFileDialog, QMessageBox
from PyQt6.QtCore import QThread, QTimer, pyqtSignal
from typing import List

from models.market_manager import MarketManager
from models.market_cache import MarketCache
from models.initializer import SystemInitializer, StockDataInitializer, ApiCheckWorker
from styles import ThemeManager, Theme
from api import HedgeFundClient


class MainController:
    """主控制器"""
    
    def __init__(self, main_window, api_base_url: str = "http://localhost:8000"):
        self.main_window = main_window
        self.api_base_url = api_base_url
        self.initializer = None
        self.init_thread = None
        self.stock_data = None
        
        # 初始化对冲基金客户端
        self.hedge_fund_client = HedgeFundClient(api_base_url)
        
        # 初始化状态变量
        self.current_stocks = []
        self.current_agents = []
        self.current_start_date = ""
        self.current_end_date = ""
        
        # 延迟初始化管理器，避免在API连接检查前触发API调用
        self.market_manager = None
        self.market_cache = MarketCache()
        
    def start_initialization(self):
        """开始系统初始化"""
        self.main_window.status_bar.update_status("正在启动系统...")
        self.main_window.loading_overlay.show()
        self.main_window.loading_overlay.raise_()
        self.main_window.loading_overlay.show_message("正在启动系统...")
        self.main_window.loading_overlay.update_status("正在检查API连接...")

        # 启动API健康检查线程
        self.api_check_thread = QThread()
        self.api_check_worker = ApiCheckWorker(self.api_base_url)
        self.api_check_worker.moveToThread(self.api_check_thread)
        self.api_check_thread.started.connect(self.api_check_worker.run)
        self.api_check_worker.finished.connect(self._on_api_check_finished)
        self.api_check_worker.finished.connect(self.api_check_thread.quit)
        self.api_check_worker.finished.connect(self.api_check_worker.deleteLater)
        self.api_check_thread.finished.connect(self.api_check_thread.deleteLater)
        self.api_check_thread.start()
        
    def _on_api_check_finished(self, is_healthy):
        if is_healthy:
            self.main_window.status_bar.update_status("API连接正常，正在初始化系统...")
            self.main_window.loading_overlay.update_status("API连接正常，正在初始化系统...")
            self.update_api_status("connected", "已连接")
            self._start_system_initialization()
        else:
            self.update_api_status("error", "连接失败")
            self._show_api_connection_error_and_exit()
        
    def _start_system_initialization(self):
        """开始系统初始化"""
        # 更新状态
        self.main_window.loading_overlay.show_message("正在初始化系统...")
        self.main_window.loading_overlay.update_status("正在创建市场管理器...")

        # 创建市场管理器（API连接检查通过后）
        self.market_manager = MarketManager(self.api_base_url)

        # 更新状态
        self.main_window.loading_overlay.update_status("正在准备数据初始化...")

        # 创建系统初始化器
        self.initializer = SystemInitializer()

        # 添加初始化任务
        stock_initializer = StockDataInitializer(self.api_base_url)
        self.initializer.add_task(stock_initializer, "股票数据")

        # 更新状态
        self.main_window.loading_overlay.update_status("正在启动初始化任务...")

        # 连接信号
        self.initializer.progress.connect(self._on_init_progress)
        self.initializer.finished.connect(self._on_init_finished)
        self.initializer.error.connect(self._on_init_error)

        # 在后台线程中运行初始化
        self.init_thread = QThread()
        self.initializer.moveToThread(self.init_thread)
        self.init_thread.started.connect(self.initializer.run)
        self.init_thread.start()
        
    def _on_init_progress(self, message: str):
        """初始化进度处理"""
        self.main_window.status_bar.update_status(message)
        self.main_window.loading_overlay.update_status(message)

        # 根据不同的初始化阶段更新加载消息
        if "系统初始化开始" in message:
            self.main_window.loading_overlay.show_message("正在初始化系统...")
        elif "正在执行初始化任务" in message:
            self.main_window.loading_overlay.show_message("正在加载数据...")
        elif "开始获取股票数据" in message:
            self.main_window.loading_overlay.show_message("正在获取股票数据...")
        elif "正在加载美股" in message:
            self.main_window.loading_overlay.show_message("正在加载美股数据...")
        elif "正在加载A股" in message:
            self.main_window.loading_overlay.show_message("正在加载A股数据...")
        elif "正在加载加密货币" in message:
            self.main_window.loading_overlay.show_message("正在加载加密货币数据...")
        elif "正在合并" in message:
            self.main_window.loading_overlay.show_message("正在整理数据...")
        elif "初始化完成" in message:
            self.main_window.loading_overlay.show_message("初始化完成")

        # 更新状态指示器
        if "API连接" in message or "连接" in message:
            if "失败" in message or "异常" in message:
                self.update_api_status("error", "连接失败")
            elif "正常" in message:
                self.update_api_status("connected", "已连接")
            else:
                self.update_api_status("unknown", "检查中...")
        
    def _on_init_finished(self):
        """初始化完成处理"""
        # 最后更新状态
        self.main_window.loading_overlay.show_message("系统初始化完成")
        self.main_window.loading_overlay.update_status("正在进入系统...")

        # 短暂延迟后隐藏加载遮罩
        QTimer.singleShot(1500, lambda: self._complete_initialization())
        
    def _complete_initialization(self):
        """完成初始化"""
        # 隐藏加载遮罩
        self.main_window.loading_overlay.hide()
        
        # 更新状态栏
        self.main_window.status_bar.update_status("系统就绪")
        
        # 获取股票数据
        self.stock_data = self.initializer.get_result("股票数据")
        
        # 生成市场列表（市场代码到市场名称）
        market_list = {}
        if self.stock_data:
            for market_code in self.stock_data:
                # 这里可根据实际需要自定义市场名称
                if market_code == "US":
                    market_list[market_code] = "美股"
                elif market_code == "CN":
                    market_list[market_code] = "A股"
                elif market_code == "CRYPTO":
                    market_list[market_code] = "加密货币"
                else:
                    market_list[market_code] = market_code
        
        # 遍历所有页面，注入市场和股票数据
        for i in range(self.main_window.page_manager.count()):
            page = self.main_window.page_manager.widget(i)
            if hasattr(page, 'stock_input') and page.stock_input:
                page.stock_input.set_market_list(market_list)
                page.stock_input.update_stock_data(self.stock_data)
            if hasattr(page, 'multi_stock_input') and page.multi_stock_input:
                page.multi_stock_input.set_market_list(market_list)
                page.multi_stock_input.update_stock_data(self.stock_data)
            # 如果页面本身就是输入组件
            if hasattr(page, 'set_market_list') and hasattr(page, 'update_stock_data'):
                page.set_market_list(market_list)
                page.update_stock_data(self.stock_data)
        
        # 启用主窗口
        # self.main_window.setEnabled(True)  # 不需要
        
        # 更新状态栏
        self.main_window.status_bar.update_status("系统初始化完成，API连接正常")
        self.update_api_status("connected", "已连接")
        
        # 清理线程
        if self.init_thread:
            self.init_thread.quit()
            self.init_thread.wait()
            self.init_thread = None
            
    def _on_init_error(self, error_message: str):
        """初始化错误处理"""
        self.main_window.loading_overlay.hide()
        # self.main_window.setEnabled(True)  # 不需要
        
        # 显示错误对话框并关闭程序
        QMessageBox.critical(
            self.main_window, 
            "初始化错误", 
            f"系统初始化失败: {error_message}\n\n程序将关闭。"
        )
        
        # 关闭程序
        self.main_window.close()
        import sys
        sys.exit(1)
        
    def _update_stock_inputs(self):
        """更新所有股票输入组件"""
        if not self.stock_data:
            return
        # 遍历所有页面
        for i in range(self.main_window.page_manager.count()):
            page = self.main_window.page_manager.widget(i)
            if hasattr(page, 'stock_input') and hasattr(page.stock_input, 'update_stock_data'):
                page.stock_input.update_stock_data(self.stock_data)
            if hasattr(page, 'multi_stock_input') and hasattr(page.multi_stock_input, 'refresh_market_data'):
                page.multi_stock_input.refresh_market_data()
            
    def handle_stock_selection(self, stocks: List[str]):
        """处理股票选择"""
        self.current_stocks = stocks
        print(f"选择的股票: {stocks}")
        
    def handle_agent_selection(self, agents: List[str]):
        """处理AI投资顾问选择"""
        self.current_agents = agents
        print(f"选择的AI投资顾问: {agents}")
        
    def handle_date_range(self, start_date: str, end_date: str):
        """处理日期范围选择"""
        self.current_start_date = start_date
        self.current_end_date = end_date
        print(f"选择的日期范围: {start_date} 到 {end_date}")
        
    def handle_start_analyze(self):
        """处理开始分析"""
        if not self.current_stocks:
            QMessageBox.warning(self.main_window, "错误", "请选择要分析的股票")
            return
            
        if not self.current_agents:
            QMessageBox.warning(self.main_window, "错误", "请选择AI投资顾问")
            return
            
        # 获取当前页面的日期范围组件
        current_page = self.main_window.page_manager.currentWidget()
        if hasattr(current_page, 'date_range') and not current_page.date_range.is_valid_range():
            QMessageBox.warning(self.main_window, "错误", "请选择有效的日期范围")
            return
            
        # 获取市场信息
        if hasattr(current_page, 'stock_input') and hasattr(current_page.stock_input, 'market_combo'):
            market = current_page.stock_input.market_combo.currentData()
        else:
            market = "未知"
        
        # 验证请求参数
        validation = self.hedge_fund_client.validate_hedge_fund_request(
            tickers=self.current_stocks,
            selected_agents=self.current_agents,
            end_date=self.current_end_date,
            start_date=self.current_start_date
        )
        
        if not validation["is_valid"]:
            error_msg = "\n".join(validation["errors"])
            QMessageBox.warning(self.main_window, "参数验证失败", f"请检查以下问题:\n{error_msg}")
            return
        
        # 显示分析进度
        agent_names = ", ".join(self.current_agents)
        stock_names = ", ".join(self.current_stocks)
        self.main_window.status_bar.update_status(f"正在分析股票: {stock_names} | 顾问: {agent_names}")
        
        # 创建分析线程
        self.analysis_thread = QThread()
        self.analysis_worker = HedgeFundAnalysisWorker(
            self.hedge_fund_client,
            self.current_stocks,
            self.current_agents,
            self.current_start_date,
            self.current_end_date
        )
        self.analysis_worker.moveToThread(self.analysis_thread)
        
        # 连接信号
        self.analysis_thread.started.connect(self.analysis_worker.run)
        self.analysis_worker.progress.connect(self._on_analysis_progress)
        self.analysis_worker.finished.connect(self._on_analysis_finished)
        self.analysis_worker.error.connect(self._on_analysis_error)
        self.analysis_worker.finished.connect(self.analysis_thread.quit)
        self.analysis_worker.finished.connect(self.analysis_worker.deleteLater)
        self.analysis_thread.finished.connect(self.analysis_thread.deleteLater)
        
        # 启动分析
        self.analysis_thread.start()
        
    def _on_analysis_progress(self, message: str):
        """分析进度处理"""
        self.main_window.status_bar.update_status(message)
        
    def _on_analysis_finished(self, result: dict):
        """分析完成处理"""
        self.main_window.status_bar.update_status("分析完成")
        
        # 显示分析结果
        self._show_analysis_results(result)
        
    def _on_analysis_error(self, error_message: str):
        """分析错误处理"""
        self.main_window.status_bar.update_status("分析失败")
        QMessageBox.critical(self.main_window, "分析错误", f"分析过程中发生错误:\n{error_message}")
        
    def _show_analysis_results(self, result: dict):
        """显示分析结果"""
        # 获取当前页面的结果视图组件
        current_page = self.main_window.page_manager.currentWidget()
        if hasattr(current_page, 'results_view'):
            current_page.results_view.display_results(result)
        else:
            # 如果没有结果视图组件，显示对话框
            decisions = result.get('decisions', {})
            signals = result.get('analyst_signals', {})
            
            result_text = f"分析完成!\n\n"
            result_text += f"投资决策数量: {len(decisions)}\n"
            result_text += f"分析师信号数量: {len(signals)}\n\n"
            
            if decisions:
                result_text += "投资决策:\n"
                for ticker, decision in decisions.items():
                    result_text += f"  {ticker}: {decision}\n"
            
            QMessageBox.information(self.main_window, "分析结果", result_text)
        
    def handle_theme_change(self, theme: Theme):
        """处理主题切换"""
        # 更新所有页面的样式
        for i in range(self.main_window.page_manager.count()):
            page = self.main_window.page_manager.widget(i)
            if hasattr(page, 'refresh_styles'):
                page.refresh_styles()
        # 更新主窗口样式
        self.main_window.refresh_styles()
        
    def handle_settings_save(self, settings: dict):
        """处理设置保存"""
        # TODO: 实现设置保存逻辑
        print(f"保存设置: {settings}")
        
    def handle_settings_load(self) -> dict:
        """处理设置加载"""
        # TODO: 实现设置加载逻辑
        return {}
        
    def handle_new(self):
        """处理新建事件"""
        # 显示确认对话框
        reply = QMessageBox.question(
            self.main_window,
            "新建分析任务",
            "是否要清空当前分析任务？\n这将清空所有输入和结果。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            # 清空股票输入
            for i in range(self.main_window.page_manager.count()):
                page = self.main_window.page_manager.widget(i)
                if hasattr(page, 'stock_input'):
                    page.stock_input.clear()
                if hasattr(page, 'multi_stock_input'):
                    page.multi_stock_input.clear()
            # 重置日期范围
            for i in range(self.main_window.page_manager.count()):
                page = self.main_window.page_manager.widget(i)
                if hasattr(page, 'date_range'):
                    page.date_range.reset()
            # 清空分析结果
            for i in range(self.main_window.page_manager.count()):
                page = self.main_window.page_manager.widget(i)
                if hasattr(page, 'results_view'):
                    page.results_view.clear()
            # 更新状态栏
            self.main_window.status_bar.update_status("已创建新的分析任务", 3000)
            # 切换到股票输入页面
            self.main_window.page_manager.setCurrentIndex(0)
            
    def handle_open(self):
        """处理打开事件"""
        # 创建文件选择对话框
        dialog = QFileDialog(self.main_window)
        dialog.setWindowTitle("打开分析任务")
        dialog.setNameFilter("分析任务文件 (*.json);;分析报告 (*.html *.pdf);;所有文件 (*.*)")
        dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        
        if dialog.exec():
            file_path = dialog.selectedFiles()[0]
            file_type = file_path.split('.')[-1].lower()
            
            try:
                if file_type == 'json':
                    # 打开分析任务配置
                    self._open_analysis_task(file_path)
                elif file_type in ['html', 'pdf']:
                    # 打开分析报告
                    self._open_analysis_report(file_path)
                else:
                    QMessageBox.warning(
                        self.main_window,
                        "打开失败",
                        f"不支持的文件类型: {file_type}"
                    )
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "打开失败",
                    f"打开文件时发生错误：\n{str(e)}"
                )
                
    def _open_analysis_task(self, file_path):
        """打开分析任务配置"""
        # TODO: 实现从JSON文件加载分析任务配置
        # 1. 加载股票列表
        # 2. 设置日期范围
        # 3. 加载分析参数
        self.main_window.status_bar.update_status(f"已打开分析任务: {file_path}", 3000)
        
    def _open_analysis_report(self, file_path):
        """打开分析报告"""
        # TODO: 实现打开HTML或PDF报告
        # 1. 如果是HTML，在内部浏览器中打开
        # 2. 如果是PDF，使用系统默认PDF查看器打开
        self.main_window.status_bar.update_status(f"已打开分析报告: {file_path}", 3000)
        
    def handle_save(self):
        """处理保存事件"""
        # 创建文件保存对话框
        dialog = QFileDialog(self.main_window)
        dialog.setWindowTitle("保存分析任务")
        dialog.setNameFilter("分析任务文件 (*.json);;分析报告 (*.html);;所有文件 (*.*)")
        dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        
        if dialog.exec():
            file_path = dialog.selectedFiles()[0]
            file_type = file_path.split('.')[-1].lower()
            
            try:
                if file_type == 'json':
                    # 保存分析任务配置
                    self._save_analysis_task(file_path)
                elif file_type == 'html':
                    # 保存分析报告
                    self._save_analysis_report(file_path)
                else:
                    QMessageBox.warning(
                        self.main_window,
                        "保存失败",
                        f"不支持的文件类型: {file_type}"
                    )
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "保存失败",
                    f"保存文件时发生错误：\n{str(e)}"
                )
                
    def _save_analysis_task(self, file_path):
        """保存分析任务配置"""
        # TODO: 实现保存分析任务配置到JSON文件
        # 1. 保存股票列表
        # 2. 保存日期范围
        # 3. 保存分析参数
        self.main_window.status_bar.update_status(f"已保存分析任务: {file_path}", 3000)
        
    def _save_analysis_report(self, file_path):
        """保存分析报告"""
        # TODO: 实现保存分析报告到HTML文件
        self.main_window.status_bar.update_status(f"已保存分析报告: {file_path}", 3000)
        
    def handle_settings(self):
        """处理设置事件"""
        # 跳转到配置管理页面
        self.main_window.page_manager.setCurrentIndex(7)  # 配置管理页面索引
        # 更新面包屑导航
        self.main_window.status_bar.update_breadcrumb("系统设置 > 配置管理")
        
    def handle_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self.main_window,
            "关于",
            "AI量化交易系统\n版本 1.0.0\n\n一个基于人工智能的量化交易系统"
        )
        
    def handle_tree_item_clicked(self, item_text: str):
        """处理树形导航项点击"""
        # 更新面包屑导航
        self.main_window.status_bar.update_breadcrumb(item_text)
        
        # 切换到对应页面
        self.main_window.page_manager.switch_to_page(item_text)
        
    def handle_chat_send(self):
        """处理对话发送事件"""
        # 获取对话输入
        current_page = self.main_window.page_manager.currentWidget()
        if hasattr(current_page, 'chat_input'):
            chat_input = current_page.chat_input.text()
            
            # 处理对话逻辑
            # 这里可以添加实际的对话处理逻辑
            print(f"收到对话输入: {chat_input}")
            
            # 清空对话输入
            current_page.chat_input.clear()
            
    def cleanup(self):
        """清理资源"""
        # 清理初始化线程
        if self.init_thread:
            self.init_thread.quit()
            self.init_thread.wait()
            self.init_thread = None
            
        # 清理市场管理器
        if self.market_manager:
            self.market_manager.close()
            
        # 清理市场缓存
        if self.market_cache:
            self.market_cache.clear_all()
        
    def update_api_status(self, status: str, message: str = ""):
        """更新API连接状态
        
        Args:
            status: 状态类型 (connected, disconnected, error, unknown)
            message: 状态消息
        """
        if hasattr(self.main_window, 'status_indicator'):
            self.main_window.status_indicator.set_status(status, message)

    def _show_api_connection_error_and_exit(self):
        """显示API连接错误并退出程序"""
        print("显示API连接错误对话框...")
        
        # 隐藏加载遮罩层
        self.main_window.loading_overlay.hide()
        
        # 确保主窗口可见
        self.main_window.show()
        self.main_window.raise_()
        self.main_window.activateWindow()
        
        # 显示错误对话框作为模态对话框
        QMessageBox.critical(
            self.main_window,
            "API连接失败",
            "无法连接到API服务器，请检查：\n\n"
            "1. 网络连接是否正常\n"
            "2. 防火墙设置是否允许连接\n\n"
            "点击确定关闭程序。",
            QMessageBox.StandardButton.Ok
        )
        
        # 设置强制关闭标志，然后关闭程序
        self.main_window._force_close = True
        self.main_window.close()
        import sys
        sys.exit(1)


class HedgeFundAnalysisWorker(QThread):
    """对冲基金分析工作线程"""
    
    # 定义信号
    progress = pyqtSignal(str)  # 进度信号
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号
    
    def __init__(self, hedge_fund_client, tickers, selected_agents, start_date, end_date):
        super().__init__()
        self.hedge_fund_client = hedge_fund_client
        self.tickers = tickers
        self.selected_agents = selected_agents
        self.start_date = start_date
        self.end_date = end_date
        
    def run(self):
        """执行分析"""
        try:
            # 定义进度回调函数
            def progress_callback(event):
                event_type = event.get("type")
                data = event.get("data", {})
                
                if event_type == "start":
                    self.progress.emit("开始对冲基金分析...")
                elif event_type == "progress":
                    agent = data.get("agent", "未知")
                    ticker = data.get("ticker", "未知")
                    status = data.get("status", "未知")
                    self.progress.emit(f"{agent} 正在分析 {ticker}: {status}")
            
            # 定义完成回调函数
            def complete_callback(result):
                self.finished.emit(result)
            
            # 定义错误回调函数
            def error_callback(error_msg):
                self.error.emit(error_msg)
            
            # 执行流式分析
            result = self.hedge_fund_client.run_hedge_fund_streaming(
                tickers=self.tickers,
                selected_agents=self.selected_agents,
                start_date=self.start_date,
                end_date=self.end_date,
                progress_callback=progress_callback,
                complete_callback=complete_callback,
                error_callback=error_callback
            )
            
            # 如果没有错误回调被调用，说明分析成功完成
            if "error" not in result:
                self.finished.emit(result)
            else:
                self.error.emit(result["error"])
                
        except Exception as e:
            self.error.emit(f"分析过程中发生异常: {str(e)}")