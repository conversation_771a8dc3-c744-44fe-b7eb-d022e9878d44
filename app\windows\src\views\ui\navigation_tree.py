from PyQt6.QtWidgets import QTreeWidget, QTreeWidgetItem
from PyQt6.QtCore import pyqtSignal
from styles import get_style


class NavigationTree(QTreeWidget):
    """导航树组件"""
    
    # 信号定义
    item_clicked = pyqtSignal(str)  # 发送点击的页面名称
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setHeaderLabel("导航")
        self.setMinimumWidth(200)
        self.setMaximumWidth(300)
        self.setStyleSheet(get_style("tree"))
        self._init_tree()
        
    def _init_tree(self):
        """初始化导航树"""
        self._create_stock_analysis_items()
        self._create_portfolio_items()
        self._create_strategy_items()
        self._create_settings_items()
        
        # 展开所有节点
        self.expandAll()
        
        # 连接信号
        self.itemClicked.connect(self._on_item_clicked)
        
    def _create_stock_analysis_items(self):
        """创建个股分析节点"""
        stock_analysis_item = QTreeWidgetItem(self, ["个股分析"])
        QTreeWidgetItem(stock_analysis_item, ["单股单顾问分析"])
        QTreeWidgetItem(stock_analysis_item, ["单股多顾问对比"])
        QTreeWidgetItem(stock_analysis_item, ["多股分析"])
        
    def _create_portfolio_items(self):
        """创建组合管理节点"""
        portfolio_item = QTreeWidgetItem(self, ["组合管理"])
        QTreeWidgetItem(portfolio_item, ["组合构建"])
        QTreeWidgetItem(portfolio_item, ["风险分析"])
        
    def _create_strategy_items(self):
        """创建策略管理节点"""
        strategy_item = QTreeWidgetItem(self, ["策略管理"])
        QTreeWidgetItem(strategy_item, ["策略列表"])
        QTreeWidgetItem(strategy_item, ["策略回测"])
        
    def _create_settings_items(self):
        """创建系统设置节点"""
        settings_item = QTreeWidgetItem(self, ["系统设置"])
        QTreeWidgetItem(settings_item, ["配置管理"])
        QTreeWidgetItem(settings_item, ["数据管理"])
        
    def _on_item_clicked(self, item: QTreeWidgetItem, column: int):
        """处理树项点击事件"""
        text = item.text(0)
        
        # 只处理叶子节点的点击
        if item.childCount() == 0:
            self.item_clicked.emit(text)
            
    def get_breadcrumb(self, item: QTreeWidgetItem) -> str:
        """获取面包屑导航路径"""
        breadcrumb_parts = []
        current_item = item
        
        # 从当前项向上遍历，构建面包屑路径
        while current_item:
            breadcrumb_parts.insert(0, current_item.text(0))
            current_item = current_item.parent()
        
        return " > ".join(breadcrumb_parts)
        
    def refresh_styles(self):
        """刷新样式"""
        self.setStyleSheet(get_style("tree")) 