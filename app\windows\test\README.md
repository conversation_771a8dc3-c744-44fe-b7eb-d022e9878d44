# Windows应用测试套件

## 📁 目录结构

```
test/
├── __init__.py                      # 测试模块初始化
├── README.md                        # 本文档
├── run_tests.py                     # 测试运行器
├── test_api_connection.py           # API连接测试
├── test_api_connection_exit.py      # API连接失败退出测试
├── test_connection_logic.py         # 连接逻辑测试
├── test_hedge_fund_client.py        # 对冲基金客户端测试
├── test_loading_states.py           # 加载状态UI测试
├── test_main_controller_states.py   # 主控制器状态测试
├── test_offline_mode.py             # 离线模式测试
├── test_ping_only.py                # 基础连通性测试
├── test_ping_only_final.py          # 最终连通性测试
└── test_styles.py                   # 样式系统测试
```

## 🚀 快速开始

### 运行所有测试
```bash
cd app/windows/test
python run_tests.py
```

### 运行特定测试
```bash
cd app/windows/test
python run_tests.py api_connection
```

### 手动运行单个测试
```bash
cd app/windows/test
python test_loading_states.py
```

## 📊 测试分类

### 🔧 单元测试
- `test_styles.py` - 样式系统测试
- `test_hedge_fund_client.py` - 对冲基金客户端测试

### 🔗 集成测试
- `test_api_connection.py` - API连接测试
- `test_api_connection_exit.py` - API连接失败退出测试
- `test_connection_logic.py` - 连接逻辑测试
- `test_offline_mode.py` - 离线模式测试
- `test_ping_only.py` - 基础连通性测试
- `test_ping_only_final.py` - 最终连通性测试

### 🎨 UI测试（需要手动运行）
- `test_loading_states.py` - 加载状态UI测试
- `test_main_controller_states.py` - 主控制器状态测试

## 📝 测试说明

### API连接测试
测试应用程序与后端API服务器的连接功能：
- 正常连接情况
- 连接失败处理
- 重试机制
- 超时处理

### 加载状态测试
测试应用程序启动时的加载状态显示：
- 多阶段状态显示
- 动态动画效果
- 状态消息切换
- 进度反馈

### 控制器测试
测试主控制器的状态管理逻辑：
- 状态消息处理
- API状态更新
- 加载遮罩动画

### 样式测试
测试应用程序的样式系统：
- 主题切换
- 样式加载
- 组件样式

## 🔧 测试环境要求

### Python依赖
```bash
pip install PyQt6 requests pydantic
```

### 可选依赖（用于完整测试）
```bash
pip install pandas numpy matplotlib
```

### 后端服务器（用于API测试）
```bash
# 在项目根目录启动后端服务器
cd ../../
python -m uvicorn src.main:app --reload --port 8000
```

## 📋 测试检查清单

### 基础功能测试
- [ ] 应用程序能正常启动
- [ ] API连接检查正常工作
- [ ] 加载状态正确显示
- [ ] 样式系统正常加载

### 错误处理测试
- [ ] API连接失败时正确处理
- [ ] 网络超时时正确处理
- [ ] 无效数据时正确处理

### 用户体验测试
- [ ] 加载状态清晰易懂
- [ ] 错误消息用户友好
- [ ] 界面响应及时

## 🐛 故障排除

### 常见问题

#### 1. 导入错误
```
ModuleNotFoundError: No module named 'xxx'
```
**解决方案：**
- 确保在 `test` 目录中运行测试
- 检查 Python 路径设置
- 安装缺失的依赖

#### 2. PyQt6错误
```
ImportError: No module named 'PyQt6'
```
**解决方案：**
```bash
pip install PyQt6
```

#### 3. API连接测试失败
**解决方案：**
- 确保后端服务器正在运行
- 检查端口8000是否被占用
- 检查防火墙设置

#### 4. UI测试无法运行
**解决方案：**
- 确保有图形界面环境
- 在Windows环境中运行
- 检查显示器连接

## 📈 测试报告

### 自动化测试
运行 `python run_tests.py` 会生成测试报告，包括：
- 通过的测试数量
- 失败的测试数量
- 错误详情
- 执行时间

### 手动测试
UI测试需要手动验证：
- 界面显示是否正确
- 动画效果是否流畅
- 用户交互是否正常

## 🔄 持续集成

### 添加新测试
1. 创建新的测试文件 `test_新功能.py`
2. 遵循现有的测试模式
3. 更新本README文档
4. 运行测试确保通过

### 测试最佳实践
1. **独立性**：每个测试应该独立运行
2. **可重复性**：测试结果应该一致
3. **清晰性**：测试目的应该明确
4. **快速性**：避免长时间运行的测试

## 📞 支持

如果遇到测试问题：
1. 查看测试输出的错误信息
2. 检查依赖是否正确安装
3. 参考故障排除部分
4. 查看相关文档
