import sys
import os
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QDateEdit, QPushButton, QComboBox, QFrame
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from styles import get_style

class DateRangeView(QWidget):
    """日期范围选择组件"""
    
    # 定义信号
    date_range_changed = pyqtSignal(str, str)  # 开始日期和结束日期变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setSpacing(8)
        
        # 第一行：快速选择
        quick_layout = QHBoxLayout()
        quick_label = QLabel("快速选择:")
        quick_label.setStyleSheet(get_style("label"))
        self.quick_select_combo = QComboBox()
        self.quick_select_combo.addItems([
            "最近一周",
            "最近一月", 
            "最近三月",
            "最近半年",
            "最近一年",
            "今年至今",
            "去年全年",
            "前年全年",
            "自定义"
        ])
        self.quick_select_combo.setStyleSheet(get_style("combobox"))
        quick_layout.addWidget(quick_label)
        quick_layout.addWidget(self.quick_select_combo)
        quick_layout.addStretch()
        layout.addLayout(quick_layout)
        
        # 第二行：开始和结束日期
        date_layout = QHBoxLayout()
        
        # 开始日期
        start_label = QLabel("开始日期:")
        start_label.setStyleSheet(get_style("label"))
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setStyleSheet(get_style("dateedit"))
        date_layout.addWidget(start_label)
        date_layout.addWidget(self.start_date)
        
        # 结束日期
        end_label = QLabel("结束日期:")
        end_label.setStyleSheet(get_style("label"))
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setStyleSheet(get_style("dateedit"))
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.end_date)
        
        # 操作按钮
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setStyleSheet(get_style("button_secondary"))
        self.reset_btn.clicked.connect(self.reset)
        date_layout.addWidget(self.reset_btn)
        
        date_layout.addStretch()
        layout.addLayout(date_layout)
        
        self.setLayout(layout)
        
        # 初始化默认日期（最近一周）
        self._init_default_dates()
        
    def _init_default_dates(self):
        """初始化默认日期"""
        today = QDate.currentDate()
        # 设置默认为最近一周
        self.start_date.setDate(today.addDays(-7))
        self.end_date.setDate(today)
        # 设置快速选择为最近一周
        self.quick_select_combo.setCurrentIndex(0)
        
    def _connect_signals(self):
        """连接信号"""
        self.quick_select_combo.currentTextChanged.connect(self._on_quick_select)
        self.start_date.dateChanged.connect(self._on_date_changed)
        self.end_date.dateChanged.connect(self._on_date_changed)
        
    def _on_quick_select(self, text: str):
        """快速选择处理"""
        today = QDate.currentDate()
        
        # 阻止信号循环
        self.start_date.blockSignals(True)
        self.end_date.blockSignals(True)
        
        if text == "最近一周":
            self.start_date.setDate(today.addDays(-7))
            self.end_date.setDate(today)
        elif text == "最近一月":
            self.start_date.setDate(today.addMonths(-1))
            self.end_date.setDate(today)
        elif text == "最近三月":
            self.start_date.setDate(today.addMonths(-3))
            self.end_date.setDate(today)
        elif text == "最近半年":
            self.start_date.setDate(today.addMonths(-6))
            self.end_date.setDate(today)
        elif text == "最近一年":
            self.start_date.setDate(today.addYears(-1))
            self.end_date.setDate(today)
        elif text == "今年至今":
            self.start_date.setDate(QDate(today.year(), 1, 1))
            self.end_date.setDate(today)
        elif text == "去年全年":
            self.start_date.setDate(QDate(today.year() - 1, 1, 1))
            self.end_date.setDate(QDate(today.year() - 1, 12, 31))
        elif text == "前年全年":
            self.start_date.setDate(QDate(today.year() - 2, 1, 1))
            self.end_date.setDate(QDate(today.year() - 2, 12, 31))
        
        # 恢复信号
        self.start_date.blockSignals(False)
        self.end_date.blockSignals(False)
        
        # 手动触发日期变化信号
        self._on_date_changed()
            
    def _on_date_changed(self):
        """日期变化处理"""
        start = self.start_date.date().toString("yyyy-MM-dd")
        end = self.end_date.date().toString("yyyy-MM-dd")
        self.date_range_changed.emit(start, end)
        
        # 检查是否与快速选择选项匹配，如果不匹配则设置为自定义
        self._check_and_update_quick_select()
        
    def _check_and_update_quick_select(self):
        """检查并更新快速选择"""
        today = QDate.currentDate()
        start = self.start_date.date()
        end = self.end_date.date()
        
        # 检查是否匹配快速选择选项
        if start == today.addDays(-7) and end == today:
            self.quick_select_combo.setCurrentText("最近一周")
        elif start == today.addMonths(-1) and end == today:
            self.quick_select_combo.setCurrentText("最近一月")
        elif start == today.addMonths(-3) and end == today:
            self.quick_select_combo.setCurrentText("最近三月")
        elif start == today.addMonths(-6) and end == today:
            self.quick_select_combo.setCurrentText("最近半年")
        elif start == today.addYears(-1) and end == today:
            self.quick_select_combo.setCurrentText("最近一年")
        elif start == QDate(today.year(), 1, 1) and end == today:
            self.quick_select_combo.setCurrentText("今年至今")
        elif start == QDate(today.year() - 1, 1, 1) and end == QDate(today.year() - 1, 12, 31):
            self.quick_select_combo.setCurrentText("去年全年")
        elif start == QDate(today.year() - 2, 1, 1) and end == QDate(today.year() - 2, 12, 31):
            self.quick_select_combo.setCurrentText("前年全年")
        else:
            self.quick_select_combo.setCurrentText("自定义")
        
    def get_date_range(self) -> tuple[str, str]:
        """获取日期范围"""
        start = self.start_date.date().toString("yyyy-MM-dd")
        end = self.end_date.date().toString("yyyy-MM-dd")
        return start, end
        
    def set_date_range(self, start_date: str, end_date: str):
        """设置日期范围"""
        try:
            start = QDate.fromString(start_date, "yyyy-MM-dd")
            end = QDate.fromString(end_date, "yyyy-MM-dd")
            if start.isValid() and end.isValid():
                # 阻止信号循环
                self.start_date.blockSignals(True)
                self.end_date.blockSignals(True)
                
                self.start_date.setDate(start)
                self.end_date.setDate(end)
                self.quick_select_combo.setCurrentText("自定义")
                
                # 恢复信号
                self.start_date.blockSignals(False)
                self.end_date.blockSignals(False)
                
                # 手动触发日期变化信号
                self._on_date_changed()
        except:
            pass
        
    def reset(self):
        """重置日期范围"""
        # 阻止信号循环
        self.start_date.blockSignals(True)
        self.end_date.blockSignals(True)
        
        # 重置为最近一周
        today = QDate.currentDate()
        self.start_date.setDate(today.addDays(-7))
        self.end_date.setDate(today)
        self.quick_select_combo.setCurrentIndex(0)
        
        # 恢复信号
        self.start_date.blockSignals(False)
        self.end_date.blockSignals(False)
        
        # 手动触发日期变化信号
        self._on_date_changed()
        
    def is_valid_range(self) -> bool:
        """检查日期范围是否有效"""
        start = self.start_date.date()
        end = self.end_date.date()
        return start <= end
        
    def refresh_styles(self):
        """刷新样式"""
        # 更新标签样式
        for child in self.findChildren(QLabel):
            child.setStyleSheet(get_style("label"))
            
        # 更新下拉框样式
        self.quick_select_combo.setStyleSheet(get_style("combobox"))
        
        # 更新日期编辑框样式
        self.start_date.setStyleSheet(get_style("dateedit"))
        self.end_date.setStyleSheet(get_style("dateedit"))
        
        # 更新按钮样式
        self.reset_btn.setStyleSheet(get_style("button_secondary")) 